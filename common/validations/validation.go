package validations

import (
	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.myteksi.net/bersama/common/validations"
)

func ValidateRequest(req interface{}) error {
	if req == nil {
		return errorwrapper.Error(apiError.BadRequest, "request is nil")
	}
	ok, err := validations.IsValid(req)
	if !ok {
		if err != nil {
			return errorwrapper.Error(apiError.BadRequest, err.Error())
		}
		return errorwrapper.Error(apiError.BadRequest, "request is invalid")
	}

	return nil
}
