package handlers

import (
	accountServiceAPI "gitlab.myteksi.net/bersama/core-banking/account-service/api"
	productMasterAPI "gitlab.myteksi.net/bersama/core-banking/product-master/api"
	customerExperienceAPI "gitlab.myteksi.net/bersama/customer-experience/api"
	customerMasterAPI "gitlab.myteksi.net/bersama/customer-master/api/v2"
	transactionHistoryAPI "gitlab.myteksi.net/bersama/transaction-history/api"
	"gitlab.myteksi.net/dakota/common/aws/s3client"
	"gitlab.myteksi.net/dakota/common/redis"
	paymentOpsTrfAPI "gitlab.myteksi.net/dakota/payment-ops-trf/api"
	payAuthZAPI "gitlab.myteksi.net/dakota/payment/pay-authz/api"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
	transactionLimitAPI "gitlab.myteksi.net/dakota/transaction-limit/api"
	customerJournalAPI "gitlab.super-id.net/bersama/corex/customer-journal/api"
	customerJourneyExperiencePreferenceAPI "gitlab.super-id.net/bersama/corex/customer-journey-experience/api"
	amlServiceAPI "gitlab.super-id.net/bersama/fintrust/aml-service/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/external/appian"
	"gitlab.super-id.net/bersama/opsce/onedash-be/external/customerportal"
	hedwigAPI "gitlab.super-id.net/bersama/opsce/onedash-be/external/hedwig"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/sqs"
	"gitlab.super-id.net/bersama/opsce/onedash-be/server/config"
)

// OnedashService serves as the context for handlers.
type OnedashService struct {
	AppConfig                                 *config.AppConfig                                       `inject:"config"`
	RedisClient                               redis.Client                                            `inject:"client.redisClient"`
	S3Client                                  s3client.S3                                             `inject:"client.s3"`
	CustomerPortalService                     customerportal.CustomerPortal                           `inject:"client.customerPortal"`
	AccountService                            accountServiceAPI.AccountService                        `inject:"client.accountService"`
	PayAuthZClient                            payAuthZAPI.PayAuthz                                    `inject:"client.payAuthZ"`
	PaymentOpsTrf                             paymentOpsTrfAPI.PaymentOpsTransfer                     `inject:"client.paymentOpsTrf"`
	HedwigClient                              hedwigAPI.Hedwig                                        `inject:"client.Hedwig"`
	CustomerExperienceClient                  customerExperienceAPI.CustomerExperience                `inject:"client.customerExperience"`
	CustomerMasterClient                      customerMasterAPI.CustomerMaster                        `inject:"client.customerMaster"`
	CustomerJournalClient                     customerJournalAPI.CustomerJournal                      `inject:"client.customerJournal"`
	CustomerJourneyExperiencePreferenceClient customerJourneyExperiencePreferenceAPI.PreferenceCenter `inject:"client.customerJourneyExperiencePreference"`
	AmlServiceCustomerClient                  amlServiceAPI.Customer                                  `inject:"client.amlServiceCustomer"`
	ProductMasterClient                       productMasterAPI.ProductMaster                          `inject:"client.productMaster"`
	TransactionHistoryClient                  transactionHistoryAPI.TxHistory                         `inject:"client.transactionHistory"`
	TransactionLimitClient                    transactionLimitAPI.TransactionLimit                    `inject:"client.transactionLimit"`
	QueueOpsUnlinkAccount                     sqs.QueueClient                                         `inject:"queue.opsUnlinkAccount"`
	QueueCrmLogAuditTrail                     sqs.QueueClient                                         `inject:"queue.crmLogAuditTrail"`
	QueueOpsTransferOnBehalf                  sqs.QueueClient                                         `inject:"queue.opsTransferOnBehalf"`
	Statsd                                    statsd.Client                                           `inject:"statsD"`
	QueueOpsBlockAccount                      sqs.QueueClient                                         `inject:"queue.opsBlockAccount"`
	QueueOpsUnblockAccount                    sqs.QueueClient                                         `inject:"queue.opsUnblockAccount"`
	QueueOpsUpdateAccountStatus               sqs.QueueClient                                         `inject:"queue.opsUpdateAccountStatus"`
	QueueOpsDeactivateLOC                     sqs.QueueClient                                         `inject:"queue.opsDeactivateLOC"`
	AppianClient                              appian.Appian                                           `inject:"client.appian"`
}
