package custom

import (
	"context"

	validator "github.com/go-playground/validator/v10"
	"gitlab.myteksi.net/bersama/common/validations"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
)

var identifierTypeMapping = map[api.IdentifierType]string{
	api.IdentifierType_PHONE_NUMBER: "required,min=10,max=16",
	api.IdentifierType_ID_NUMBER:    "required,numeric,len=16",
}

var allowedTypes = map[string][]api.IdentifierType{
	"GetCustomersRequest": {
		api.IdentifierType_PHONE_NUMBER,
		api.IdentifierType_NAME,
		api.IdentifierType_ID_NUMBER,
		api.IdentifierType_SAFE_ID,
		api.IdentifierType_CIF,
		api.IdentifierType_ACCOUNT_NUMBER,
	},
	"CustomerSearchRequest": {
		api.IdentifierType_PHONE_NUMBER,
		api.IdentifierType_NAME,
		api.IdentifierType_ID_NUMBER,
		api.IdentifierType_SAFE_ID,
		api.IdentifierType_CIF,
		api.IdentifierType_ACCOUNT_NUMBER,
	},
}

// IdentifierTypeValueValidation ...
func IdentifierTypeValueValidation(fl validations.FieldLevel) bool {
	requestType := fl.Parent().Type().Name()
	identifierType := api.IdentifierType(fl.Parent().FieldByName("IdentifierType").String())
	validate := validator.New()

	if identifierType != "" {
		if !isAllowedType(requestType, identifierType) {
			slog.FromContext(context.Background()).Error("IdentifierTypeValueValidation", "identifier type is not allowed")
			return false
		}

		rule, exists := identifierTypeMapping[identifierType]
		if !exists {
			return true
		}

		identifier, ok := fl.Field().Interface().(api.IdentifierType)
		if !ok {
			slog.FromContext(context.Background()).Error("IdentifierTypeValueValidation", "error getting identifier value")
			return false
		}

		err := validate.Var(identifier, rule)
		if err != nil {
			slog.FromContext(context.Background()).Error("IdentifierTypeValueValidation", "identifier doesn't follow correct format")
			return false
		}
	}

	return true
}

func isAllowedType(requestType string, idType api.IdentifierType) bool {
	allowed, exists := allowedTypes[requestType]
	if !exists {
		return false
	}

	for _, t := range allowed {
		if t == idType {
			return true
		}
	}
	return false
}
