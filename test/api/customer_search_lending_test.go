package api

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"regexp"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage/mocks"
	accountServiceAPI "gitlab.myteksi.net/bersama/core-banking/account-service/api"
	accountServiceMock "gitlab.myteksi.net/bersama/core-banking/account-service/api/mock"
	productMasterAPI "gitlab.myteksi.net/bersama/core-banking/product-master/api"
	productMasterMock "gitlab.myteksi.net/bersama/core-banking/product-master/api/mock"
	customerExperience "gitlab.myteksi.net/bersama/customer-experience/api"
	customerExperienceMock "gitlab.myteksi.net/bersama/customer-experience/api/mock"
	customerMaster "gitlab.myteksi.net/bersama/customer-master/api/v2"
	customerMasterMock "gitlab.myteksi.net/bersama/customer-master/api/v2/mock"
	redisMock "gitlab.myteksi.net/dakota/common/redis/mocks"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
	loanAppMock "gitlab.myteksi.net/dakota/lending/loan-app/api/mock"
	loanExpServiceAPI "gitlab.myteksi.net/dakota/lending/loan-exp/api"
	loanExpMock "gitlab.myteksi.net/dakota/lending/loan-exp/api/mock"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/db/redis"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/logic"
	"gitlab.super-id.net/bersama/opsce/onedash-be/test/resources"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils/jwt"
)

var _ = Describe("Customer Search Lending", func() {
	var (
		db                 *sql.DB
		mocker             sqlmock.Sqlmock
		mockLoanExpService *loanExpMock.LoanExp
		mockLoanAppService *loanAppMock.LoanApp
	)

	BeforeEach(func() {
		var err error
		db, mocker, err = sqlmock.New()
		Expect(err).NotTo(HaveOccurred())

		mockLoanExpService = &loanExpMock.LoanExp{}
		mockLoanAppService = &loanAppMock.LoanApp{}
		mockProductMaster = &productMasterMock.ProductMaster{}
		mockAccountService = &accountServiceMock.AccountService{}
		mockCustomerMaster = &customerMasterMock.CustomerMaster{}
		mockCustomerExperience = &customerExperienceMock.CustomerExperience{}
		mockRedis = &redisMock.Client{}

		config := &logic.MockProcessConfig{
			LoanExp:            mockLoanExpService,
			LoanApp:            mockLoanAppService,
			ProductMaster:      mockProductMaster,
			AccountService:     mockAccountService,
			CustomerMaster:     mockCustomerMaster,
			CustomerExperience: mockCustomerExperience,
			AppConfig:          service.AppConfig,
		}

		logic.MockInitLogic(config)
		mockStorage = &mocks.MockDatabaseStore{}
		commonStorage.DBStoreD = mockStorage
		redis.RedisClient = mockRedis
	})

	AfterEach(func() {
		db.Close()
	})

	Context("CustomerSearch for Lending", func() {
		When("user has valid permissions and getting LOC information", func() {
			It("should successfully return LOC information", func() {
				// Create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "c0575d1f-97a6-494d-8228-42711ae1fdea",
    				"identifierType": "SAFE_ID",
   					"key": "locInformation",
					"payload": {
						"locAccountID": "LOC123456789",
						"safeID": "c0575d1f-97a6-494d-8228-42711ae1fdea",
						"activeLoanAccountID": "LOAN123,LOAN456",
						"cif": "CIF123456789"
					}
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
						AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("locInformation", 1). // keyword and status
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id",
						"order", "level", "status", "type",
					}).AddRow(
						1, "LOC Information", "locInformation", nil, 1, 2, 1, "section",
					))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRole)).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order"}).
						AddRow(1, "Account ID", "locAccountID", "oneliner", 1, 1).
						AddRow(2, "Status", "locStatus", "oneliner", 1, 2).
						AddRow(3, "Credit Limit", "locTotalCreditLimit", "oneliner", 1, 3).
						AddRow(4, "Available Credit", "locAvailableCreditLimit", "oneliner", 1, 4).
						AddRow(5, "Product Variant", "locProductVariant", "oneliner", 1, 5))

				createdAt, _ := time.Parse(time.RFC3339, "2023-01-01T00:00:00Z")

				mockRedis.On("GetString", mock.Anything, fmt.Sprintf(constants.FetchAccountDetailsRedisKey, "LOC123456789", constants.ProductVariantLineOfCredit, "c0575d1f-97a6-494d-8228-42711ae1fdea")).Return("", nil)

				// Mock for FetchAccountDetails
				mockLoanExpService.On("FetchAccountDetailsForCRM", mock.Anything, mock.Anything).Return(&loanExpServiceAPI.AccountDetailsResponse{
					AccountID:          "LOC123456789",
					ProductVariantCode: constants.ProductVariantLineOfCredit,
					CurrentStatus:      "ACTIVE",
					SubStatus:          "NORMAL",
					CreatedAt:          createdAt,
					Parameters: map[string]interface{}{
						"offeredLOC": map[string]interface{}{
							"val": float64(********00),
						},
						"availableLOC": map[string]interface{}{
							"val": float64(*********),
						},
						"offeredInterestRate": float64(12.5),
						"offeredMaxTenor":     float64(12),
						"offeredEIR":          float64(13.2),
						"expiryDate":          "2024-01-01",
						"currentDueAmountWithDueDate": map[string]interface{}{
							"val": float64(********),
						},
					},
				}, nil).Once()

				mockRedis.On("Set", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(true, nil)

				// Mock for GetCustomerInsights
				mockLoanExpService.On("FetchCustomerInsightsForCRM", mock.Anything, mock.Anything).Return(&loanExpServiceAPI.FetchCustomerInsightsForCRMResponse{
					DueInDays: 5,
					TotalDueAmount: &loanExpServiceAPI.Money{
						Val: 500000,
					},
					TotalPendingRepaymentAmount: &loanExpServiceAPI.Money{
						Val: 9500000,
					},
				}, nil).Once()

				// Mock for GetLOCHoldCode
				mockLoanExpService.On("GetHoldCode", mock.Anything, mock.Anything).Return(&loanExpServiceAPI.GetHoldCodesResponse{
					AccountID:         "LOC123456789",
					IsDrawdownBlocked: false,
					ReasonCodes:       []string{},
				}, nil).Once()

				// Mock for GetLoanInstructions
				mockProductMaster.On("GetLoanInstructionsByCode", mock.Anything, mock.Anything).Return(&productMasterAPI.GetLoanInstructionsByCodeResponse{
					LoanInstruction: []productMasterAPI.LoanInstruction{
						{
							Code: "TEMP_BLOCK",
							Name: "Temporary Block",
						},
						{
							Code: "PERM_BLOCK",
							Name: "Permanent Block",
						},
					},
				}, nil).Once()

				expectedResponse := `{
					"status": "Success",
					"structure": {
						"key": "locInformation",
						"type": "section",
						"label": "LOC Information",
						"children": [
							{
								"key": "locAccountID",
								"type": "oneliner",
								"label": "Account ID"
							},
							{
								"key": "locStatus",
								"type": "oneliner",
								"label": "Status"
							},
							{
								"key": "locTotalCreditLimit",
								"type": "oneliner",
								"label": "Credit Limit"
							},
							{
								"key": "locAvailableCreditLimit",
								"type": "oneliner",
								"label": "Available Credit"
							},
							{
								"key": "locProductVariant",
								"type": "oneliner",
								"label": "Product Variant"
							}
						]
					},
					"data": {
						"cif": "CIF123456789",
						"locAccountID": "LOC123456789",
						"locAvailableCreditLimit": "8,000,000.00",
						"locProductVariant": "DEFAULT_FLEXI_LOAN_LINE_OF_CREDIT",
						"locStatus": "ACTIVE",
						"locTotalCreditLimit": "10,000,000.00",
						"meta": null,
						"safeID": "c0575d1f-97a6-494d-8228-42711ae1fdea"
					}
				}`

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
				Expect(response.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("user has valid permissions and getting active loan list", func() {
			It("should successfully return active loan list", func() {
				// Create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "c0575d1f-97a6-494d-8228-42711ae1fdea",
					"identifierType": "SAFE_ID",
					"key": "activeLoanList",
					"payload": {
						"locAccountID": "LOC123456789",
						"loanAccountID": "LOAN123,LOAN456",
						"safeID": "c0575d1f-97a6-494d-8228-42711ae1fdea",
						"cif": "CIF123456789"
					}
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
						AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("activeLoanList", 1). // keyword and status
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id",
						"order", "level", "status", "type",
					}).AddRow(
						1, "Active Loan List", "activeLoanList", nil, 1, 2, 1, "sectionArray",
					))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRole)).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order"}).
						AddRow(1, "Loan Account Number", "loanAccountNumber", "oneliner", 1, 1).
						AddRow(2, "Status", "loanStatus", "oneliner", 1, 2).
						AddRow(3, "Loan Purpose", "loanPurpose", "oneliner", 1, 3))

				// Mock for GetAccountDetailsByAccountID
				mockAccountService.On("GetAccountDetailsByAccountID", mock.Anything, &accountServiceAPI.GetAccountRequest{
					AccountID: "LOAN123",
				}).Return(&accountServiceAPI.GetAccountResponse{
					Account: &accountServiceAPI.Account{
						Id:              "LOAN123",
						ParentAccountID: "LOC123456789",
					},
				}, nil).Once()

				mockAccountService.On("GetAccountDetailsByAccountID", mock.Anything, &accountServiceAPI.GetAccountRequest{
					AccountID: "LOAN456",
				}).Return(&accountServiceAPI.GetAccountResponse{
					Account: &accountServiceAPI.Account{
						Id:              "LOAN456",
						ParentAccountID: "LOC123456789",
					},
				}, nil).Once()

				createdAt, _ := time.Parse(time.RFC3339, "2023-01-01T00:00:00Z")

				mockRedis.On("GetString", mock.Anything, mock.Anything).Return("", nil)

				// Mock for FetchAccountDetails for LOAN123
				mockLoanExpService.On("FetchAccountDetailsForCRM", mock.Anything, mock.MatchedBy(func(req *loanExpServiceAPI.AccountDetailsRequestForCRM) bool {
					return req.AccountID == "LOAN123"
				})).Return(&loanExpServiceAPI.AccountDetailsResponse{
					AccountID:          "LOAN123",
					ProductVariantCode: "LOAN_ACCOUNT",
					CurrentStatus:      "ACTIVE",
					CreatedAt:          createdAt,
					Parameters: map[string]interface{}{
						"loanName":          "Renovation",
						"daysPastDueBucket": "1",
						"totalPendingRepaymentAmount": map[string]interface{}{
							"val": float64(5000000),
						},
						"tenorValue":             "12",
						"lastInstallmentDueDate": "2024-01-01",
						"totalPaidAmount": map[string]interface{}{
							"val": float64(1000000),
						},
					},
				}, nil).Once()

				mockRedis.On("Set", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(true, nil)

				mockRedis.On("GetString", mock.Anything, mock.Anything).Return("", nil)

				// Mock for FetchAccountDetails for LOAN456
				mockLoanExpService.On("FetchAccountDetailsForCRM", mock.Anything, mock.MatchedBy(func(req *loanExpServiceAPI.AccountDetailsRequestForCRM) bool {
					return req.AccountID == "LOAN456"
				})).Return(&loanExpServiceAPI.AccountDetailsResponse{
					AccountID:          "LOAN456",
					ProductVariantCode: constants.ProductVariantLoanAccount,
					CurrentStatus:      "ACTIVE",
					CreatedAt:          createdAt,
					Parameters: map[string]interface{}{
						"loanName":          "Business",
						"daysPastDueBucket": "0",
						"totalPendingRepaymentAmount": map[string]interface{}{
							"val": float64(8000000),
						},
						"tenorValue":             "24",
						"lastInstallmentDueDate": "2025-02-01",
						"totalPaidAmount": map[string]interface{}{
							"val": float64(2000000),
						},
					},
				}, nil).Once()

				mockRedis.On("Set", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(true, nil)

				expectedResponse := `{
					"status": "Success",
					"structure": {
						"key": "activeLoanList",
						"type": "sectionArray",
						"label": "Active Loan List",
						"children": [
							{
								"key": "loanAccountNumber",
								"type": "oneliner",
								"label": "Loan Account Number"
							},
							{
								"key": "loanStatus",
								"type": "oneliner",
								"label": "Status"
							},
							{
								"key": "loanPurpose",
								"type": "oneliner",
								"label": "Loan Purpose"
							}
						]
					},
					"data": {
						"activeLoanList": [
							{
								"activeLoanList": null,
								"loanAccountNumber": "LOAN123",
								"loanPurpose": "Renovation",
								"loanStatus": "ACTIVE"
							},
							{
								"activeLoanList": null,
								"loanAccountNumber": "LOAN456",
								"loanPurpose": "Business",
								"loanStatus": "ACTIVE"
							}
						],
						"cif": "CIF123456789",
						"meta": null,
						"safeID": "c0575d1f-97a6-494d-8228-42711ae1fdea"
					}
				}`

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
				Expect(response.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("user has valid permissions and getting loan detail information", func() {
			It("should successfully return loan detail information", func() {
				// Create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "c0575d1f-97a6-494d-8228-42711ae1fdea",
					"identifierType": "SAFE_ID",
					"key": "activeLoanListDetails",
					"payload": {
						"loanAccountID": "LOAN123",
						"safeID": "c0575d1f-97a6-494d-8228-42711ae1fdea"
					}
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
						AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("activeLoanListDetails", 1). // keyword and status
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id",
						"order", "level", "status", "type",
					}).AddRow(
						1, "Loan Detail Information", "activeLoanListDetails", nil, 1, 3, 1, "section",
					))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRole)).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order"}).
						AddRow(1, "Product Variant", "loanDetailProductVariant", "oneliner", 1, 1).
						AddRow(2, "Disbursement Date", "loanDetailDisbursementDate", "oneliner", 1, 2).
						AddRow(3, "Disbursement Amount", "loanDetailDisbursementAmount", "oneliner", 1, 3))

				createdAt, _ := time.Parse(time.RFC3339, "2023-01-01T00:00:00Z")

				mockRedis.On("GetString", mock.Anything, mock.Anything).Return("", nil)

				// Mock for FetchAccountDetails
				mockLoanExpService.On("FetchAccountDetailsForCRM", mock.Anything, mock.Anything).Return(&loanExpServiceAPI.AccountDetailsResponse{
					AccountID:          "LOAN123",
					ProductVariantCode: "LOAN_ACCOUNT",
					SubStatus:          "NORMAL",
					CreatedAt:          createdAt,
					Parameters: map[string]interface{}{
						"principalAmount": map[string]interface{}{
							"val": float64(********),
						},
						"totalPendingRepaymentAmount": map[string]interface{}{
							"val": float64(9000000),
						},
						"totalInterestSavingsAmount": map[string]interface{}{
							"val": float64(500000),
						},
						"preferredRepaymentDayOfMonth": 15,
						"currentDueAmountWithDueDate": []interface{}{
							map[string]interface{}{
								"dueAmount": map[string]interface{}{
									"val": float64(1000000),
								},
								"dueDate": "2023-03-15",
							},
						},
						"lastUpdatedDate": "2023-02-15",
						"recoveryAmount": map[string]interface{}{
							"val": float64(0),
						},
						"expiryDate":        "2024-01-01",
						"daysPastDueBucket": "0",
						"installmentDetails": []interface{}{
							map[string]interface{}{
								"installmentSequenceNumber": 1,
								"installmentDueDate":        "2023-02-15",
								"installmentPendingBalance": map[string]interface{}{
									"val": float64(0),
								},
								"principalPendingBalance": map[string]interface{}{
									"val": float64(0),
								},
								"normalInterestPendingBalance": map[string]interface{}{
									"val": float64(0),
								},
								"penalInterestPendingBalance": map[string]interface{}{
									"val": float64(0),
								},
								"daysPastDue": 0,
								"updatedAt":   "2023-02-15",
								"status":      "PAID",
								"installmentBalancePaid": map[string]interface{}{
									"val": float64(1000000),
								},
								"principalPaid": map[string]interface{}{
									"val": float64(900000),
								},
								"normalInterestPaid": map[string]interface{}{
									"val": float64(100000),
								},
								"penalInterestPaid": map[string]interface{}{
									"val": float64(0),
								},
							},
							map[string]interface{}{
								"installmentSequenceNumber": 2,
								"installmentDueDate":        "2023-03-15",
								"installmentPendingBalance": map[string]interface{}{
									"val": float64(1000000),
								},
								"principalPendingBalance": map[string]interface{}{
									"val": float64(900000),
								},
								"normalInterestPendingBalance": map[string]interface{}{
									"val": float64(100000),
								},
								"penalInterestPendingBalance": map[string]interface{}{
									"val": float64(0),
								},
								"daysPastDue": 0,
								"updatedAt":   "2023-02-15",
								"status":      "PENDING",
								"installmentBalancePaid": map[string]interface{}{
									"val": float64(0),
								},
								"principalPaid": map[string]interface{}{
									"val": float64(0),
								},
								"normalInterestPaid": map[string]interface{}{
									"val": float64(0),
								},
								"penalInterestPaid": map[string]interface{}{
									"val": float64(0),
								},
							},
						},
					},
				}, nil).Once()

				mockRedis.On("Set", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(true, nil)

				expectedResponse := `{
					"status": "Success",
					"structure": {
						"key": "activeLoanListDetails",
						"type": "section",
						"label": "Loan Detail Information",
						"children": [
							{
								"key": "loanDetailProductVariant",
								"type": "oneliner",
								"label": "Product Variant"
							},
							{
								"key": "loanDetailDisbursementDate",
								"type": "oneliner",
								"label": "Disbursement Date"
							},
							{
								"key": "loanDetailDisbursementAmount",
								"type": "oneliner",
								"label": "Disbursement Amount"
							}
						]
					},
					"data": {
						"cif": null,
						"loanDetailDisbursementAmount": "100,000.00",
						"loanDetailDisbursementDate": "2023-01-01T00:00:00Z",
						"loanDetailProductVariant": "LOAN_ACCOUNT",
						"meta": null,
						"safeID": "c0575d1f-97a6-494d-8228-42711ae1fdea"
					}
				}`

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
				Expect(response.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("user has valid permissions and getting address loan", func() {
			It("should successfully return address loan information", func() {
				// Create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "c0575d1f-97a6-494d-8228-42711ae1fdea",
					"identifierType": "SAFE_ID",
					"key": "addressLoan",
					"payload": {
						"loanAccountID": "LOAN123",
						"safeID": "c0575d1f-97a6-494d-8228-42711ae1fdea"
					}
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
						AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("addressLoan", 1). // keyword and status
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id",
						"order", "level", "status", "type",
					}).AddRow(
						1, "Address Loan", "addressLoan", nil, 1, 3, 1, "section",
					))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRole)).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order"}).
						AddRow(1, "Jalan", "loanStreet", "oneliner", 1, 1).
						AddRow(2, "Kota", "loanKota", "oneliner", 1, 2).
						AddRow(3, "RT", "loanRT", "oneliner", 1, 3).
						AddRow(4, "RW", "loanRW", "oneliner", 1, 4))

				// expectedResponseForGetCustomerOps ...
				var expectedResponseForGetCustomerOps = `{
					"items": [
						{
							"customer": {
								"ID": "c0575d1f-97a6-494d-8228-42711ae1fdea",
								"addresses": [
									{
										"addressType": "REGISTERED",
										"street": "JL RAYA CISEENG NO. 12 BLOK A",
										"city": "KABUPATEN BOGOR",
										"country": "ID",
										"postalCode": "16120",
										"province": "JAWA BARAT",
										"subdistrict": "CISEENG",
										"village": "CIBENTANG",
										"rt": "001",
										"rw": "002",
										"provinceKey": "JAWA BARAT_32",
										"cityKey": "BOGOR_32.01",
										"subdistrictKey": "CISEENG_32.01.33",
										"villageKey": "CIBENTANG_32.01.33.2004",
										"postalCodeKey": "16120_32.01.33.2004"
									},
									{
										"addressType": "MAILING",
										"street": "JL RAYA CISEENG NO. 12 BLOK A",
										"city": "KABUPATEN BOGOR",
										"country": "ID",
										"postalCode": "16120",
										"province": "JAWA BARAT",
										"subdistrict": "CISEENG",
										"village": "CIBENTANG",
										"rt": "001",
										"rw": "002",
										"provinceKey": "JAWA BARAT_32",
										"cityKey": "BOGOR_32.01",
										"subdistrictKey": "CISEENG_32.01.33",
										"villageKey": "CIBENTANG_32.01.33.2004",
										"postalCodeKey": "16120_32.01.33.2004"
									}
								]
							}
						}
					]
				}`

				var mockResponseOps *customerExperience.GetCustomerOpsResponse
				_ = json.Unmarshal([]byte(expectedResponseForGetCustomerOps), &mockResponseOps)

				mockCustomerExperience.On("GetCustomerOps", mock.Anything, &customerExperience.GetCustomerOpsRequest{
					Identifier:     "c0575d1f-97a6-494d-8228-42711ae1fdea",
					IdentifierType: "SAFE_ID",
					Page:           1,
				}).Return(mockResponseOps, nil).Once()

				expectedResponse := `{
					"status": "Success",
					"structure": {
						"key": "addressLoan",
						"type": "section",
						"label": "Address Loan",
						"children": [
							{
								"key": "loanStreet",
								"type": "oneliner",
								"label": "Jalan"
							},
							{
								"key": "loanKota",
								"type": "oneliner",
								"label": "Kota"
							},
							{
								"key": "loanRT",
								"type": "oneliner",
								"label": "RT"
							},
							{
								"key": "loanRW",
								"type": "oneliner",
								"label": "RW"
							}
						]
					},
					"data": {
						"cif": null,
              			"loanStreet": "JL RAYA CISEENG NO. 12 BLOK A",
						"loanKota": "KABUPATEN BOGOR",
						"loanRT": "001",
						"loanRW": "002",
						"meta": null,
						"safeID": "c0575d1f-97a6-494d-8228-42711ae1fdea"
					}
				}`

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
				Expect(response.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Lending", func() {
			It("should successfully return customer search results", func() {
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
					"identifierType": "SAFE_ID",
					"key": "lending"
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
						AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("lending", 1). // keyword and status
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id", "order", "level", "status", "type",
					}).
						AddRow(1, "Lending", "lending", nil, 1, 1, 1, "tab"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRole)).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order",
					}).
						AddRow(1, "Term Loan - Partner", "lending.termLoanPartner", "subTab", 1, 1))

				// expectedResponseForGetCustomerLean ...
				var expectedResponseForGetCustomerLean = `{
					"customer": {
						"data": {
							"ID": "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
							"publicID": "ID198980161816",
							"name": "Mrs Nathanael Buckridge",
							"gender": "MALE",
							"dateOfBirth": "1998-08-18",
							"nationality": "WNI",
							"status": "ONBOARDED",
							"type": "NAT_PERSON",
							"startDate": "2024-01-30T04:38:20Z",
							"relatedCounterPartyInd": false,
							"maritalStatus": "SINGLE",
							"motherMaidenName": "superbank",
							"placeOfBirth": "PEMALANG"
						}
					}
				}`

				var mockResponse *customerMaster.GetCustomerByIdentifierResponse
				_ = json.Unmarshal([]byte(expectedResponseForGetCustomerLean), &mockResponse)

				// mock for GetCustomerLean
				mockCustomerMaster.On("GetCustomerLean", mock.Anything, &customerMaster.GetCustomerLeanRequest{
					Identifier:     "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
					IdentifierType: customerMaster.IdentifierType_SAFE_ID,
					ServiceID:      constants.DIGIBANK,
					Data:           []customerMaster.DataFetchedType{customerMaster.Data_PersonalInfo},
				}).Return(mockResponse, nil).Once()

				expectedResponse := `{
					"status": "Success",
					"structure": {
						"key": "lending",
						"type": "tab",
						"label": "Lending",
						"children": [
							{
								"key": "lending.termLoanPartner",
								"type": "subTab",
								"label": "Term Loan - Partner",
								"hasChildren": true
							}
						]
					},
					"data": {
						"cif": "ID198980161816",
						"meta": null,
						"safeID": "e09e655a-4a6d-47b0-9078-ffec9cbf107f"
					}
				}`

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
				Expect(response.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Term Loan - Partner", func() {
			It("should successfully return customer search results", func() {
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
					"identifierType": "SAFE_ID",
					"key": "lending.termLoanPartner"
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
						AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("lending.termLoanPartner", 1). // keyword and status
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id", "order", "level", "status", "type",
					}).
						AddRow(1, "Term Loan - Partner", "lending.termLoanPartner", nil, 1, 2, 1, "subTab"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRole)).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order",
					}).
						AddRow(1, "Active Loan List", "lending.termLoanPartner.activeLoanList", "table", 1, 1).
						AddRow(2, "Inactive Loan List", "lending.termLoanPartner.inactiveLoanList", "table", 1, 2))

				expectedResponse := `{
					"status": "Success",
					"structure": {
						"key": "lending.termLoanPartner",
						"type": "subTab",
						"label": "Term Loan - Partner",
						"children": [
						{
							"key": "lending.termLoanPartner.activeLoanList",
							"type": "table",
							"label": "Active Loan List",
							"hasChildren": true
						},
						{
							"key": "lending.termLoanPartner.inactiveLoanList",
							"type": "table",
							"label": "Inactive Loan List",
							"hasChildren": true
						}
						]
					},
					"data": {
						"cif": null,
						"meta": null,
						"safeID": "e09e655a-4a6d-47b0-9078-ffec9cbf107f"
					}
				}`

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
				Expect(response.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("Active Loan List", func() {
			It("should successfully return customer search results", func() {
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
					"identifierType": "SAFE_ID",
					"key": "lending.termLoanPartner.activeLoanList"
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
						AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("lending.termLoanPartner.activeLoanList", 1). // keyword and status
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id", "order", "level", "status", "type",
					}).
						AddRow(1, "Active Loan List", "lending.termLoanPartner.activeLoanList", nil, 3, 2, 1, "table"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRole)).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order",
					}).
						AddRow(1, "Loan Account No.", "lending.termLoanPartner.activeLoanList.loanDetail", "oneliner", 1, 1).
						AddRow(2, "Akulaku Loan ID", "lending.termLoanPartner.activeLoanList.akulakuLoanID", "oneliner", 1, 2).
						AddRow(3, "Partner", "lending.termLoanPartner.activeLoanList.partner", "oneliner", 1, 3).
						AddRow(4, "Product Type", "lending.termLoanPartner.activeLoanList.productType", "oneliner", 1, 4).
						AddRow(5, "Loan Period", "lending.termLoanPartner.activeLoanListLoan.period", "oneliner", 1, 5).
						AddRow(6, "Interest Rate (p.m.)", "lending.termLoanPartner.activeLoanList.interestRate", "oneliner", 1, 6).
						AddRow(7, "Disbursement Date", "lending.termLoanPartner.activeLoanList.disbursementDate", "oneliner", 1, 7).
						AddRow(8, "Maturity Date", "lending.termLoanPartner.activeLoanList.maturityDate", "oneliner", 1, 8).
						AddRow(9, "Principal", "lending.termLoanPartner.activeLoanList.principal", "oneliner", 1, 9).
						AddRow(10, "Total Interest Amount", "lending.termLoanPartner.activeLoanList.totalInterestAmount", "oneliner", 1, 10).
						AddRow(11, "Total Installment Fee", "lending.termLoanPartner.activeLoanList.totalInstallmentFee", "oneliner", 1, 11).
						AddRow(12, "Outstanding Principal", "lending.termLoanPartner.activeLoanList.outstandingPrincipal", "oneliner", 1, 12).
						AddRow(13, "Outstanding Interest", "lending.termLoanPartner.activeLoanList.outstandingInterest", "oneliner", 1, 13).
						AddRow(14, "Outstanding Fee", "lending.termLoanPartner.activeLoanList.outstandingFee", "oneliner", 1, 14).
						AddRow(15, "Installment Amount", "lending.termLoanPartner.activeLoanList.installmentAmount", "oneliner", 1, 15).
						AddRow(16, "Next Installment Date", "lending.termLoanPartner.activeLoanList.nextInstallmentDate", "oneliner", 1, 16).
						AddRow(17, "Overdue Amount", "lending.termLoanPartner.activeLoanList.overdueAmount", "oneliner", 1, 17).
						AddRow(18, "Collectibility", "lending.termLoanPartner.activeLoanList.collectibility", "oneliner", 1, 18).
						AddRow(19, "DPD", "lending.termLoanPartner.activeLoanList.dpd", "oneliner", 1, 19).
						AddRow(20, "Status", "lending.termLoanPartner.activeLoanList.loanStatus", "oneliner", 1, 20).
						AddRow(21, "Detail", "lending.termLoanPartner.activeLoanList.loanDetail", "sectionDetail", 1, 21))

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
			})
		})

		When("Inactive Loan List", func() {
			It("should successfully return customer search results", func() {
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
					"identifierType": "SAFE_ID",
					"key": "lending.termLoanPartner.inactiveLoanList"
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
						AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("lending.termLoanPartner.inactiveLoanList", 1). // keyword and status
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id", "order", "level", "status", "type",
					}).
						AddRow(1, "Inactive Loan List", "lending.termLoanPartner.inactiveLoanList", nil, 3, 2, 1, "table"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRole)).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order",
					}).
						AddRow(1, "Loan Account No.", "lending.termLoanPartner.inactiveLoanList.loanDetail", "oneliner", 1, 1).
						AddRow(2, "Akulaku Loan ID", "lending.termLoanPartner.inactiveLoanList.akulakuLoanID", "oneliner", 1, 2).
						AddRow(3, "Partner", "lending.termLoanPartner.inactiveLoanList.partner", "oneliner", 1, 3).
						AddRow(4, "Product Type", "lending.termLoanPartner.inactiveLoanList.productType", "oneliner", 1, 4).
						AddRow(5, "Loan Period", "lending.termLoanPartner.inactiveLoanListLoan.period", "oneliner", 1, 5).
						AddRow(6, "Interest Rate (p.m.)", "lending.termLoanPartner.inactiveLoanList.interestRate", "oneliner", 1, 6).
						AddRow(7, "Disbursement Date", "lending.termLoanPartner.inactiveLoanList.disbursementDate", "oneliner", 1, 7).
						AddRow(8, "Maturity Date", "lending.termLoanPartner.inactiveLoanList.maturityDate", "oneliner", 1, 8).
						AddRow(9, "Principal", "lending.termLoanPartner.inactiveLoanList.principal", "oneliner", 1, 9).
						AddRow(10, "Total Interest Amount", "lending.termLoanPartner.inactiveLoanList.totalInterestAmount", "oneliner", 1, 10).
						AddRow(11, "Total Installment Fee", "lending.termLoanPartner.inactiveLoanList.totalInstallmentFee", "oneliner", 1, 11).
						AddRow(12, "Outstanding Principal", "lending.termLoanPartner.inactiveLoanList.outstandingPrincipal", "oneliner", 1, 12).
						AddRow(13, "Outstanding Interest", "lending.termLoanPartner.inactiveLoanList.outstandingInterest", "oneliner", 1, 13).
						AddRow(14, "Outstanding Fee", "lending.termLoanPartner.inactiveLoanList.outstandingFee", "oneliner", 1, 14).
						AddRow(15, "Installment Amount", "lending.termLoanPartner.inactiveLoanList.installmentAmount", "oneliner", 1, 15).
						AddRow(16, "Next Installment Date", "lending.termLoanPartner.inactiveLoanList.nextInstallmentDate", "oneliner", 1, 16).
						AddRow(17, "Overdue Amount", "lending.termLoanPartner.inactiveLoanList.overdueAmount", "oneliner", 1, 17).
						AddRow(18, "Collectibility", "lending.termLoanPartner.inactiveLoanList.collectibility", "oneliner", 1, 18).
						AddRow(19, "DPD", "lending.termLoanPartner.inactiveLoanList.dpd", "oneliner", 1, 19).
						AddRow(20, "Status", "lending.termLoanPartner.inactiveLoanList.loanStatus", "oneliner", 1, 20).
						AddRow(21, "Detail", "lending.termLoanPartner.inactiveLoanList.loanDetail", "sectionDetail", 1, 21))

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
			})
		})

		When("Active Loan Account Number Information", func() {
			It("should successfully return loan account number data", func() {
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				Expect(err).ShouldNot(HaveOccurred())

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
					"identifierType": "SAFE_ID",
					"key": "lending.termLoanPartner.activeLoanList.loanDetail",
					"payload": {
						"loanAccountNumber": "1"
					}
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
						AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("lending.termLoanPartner.activeLoanList.loanDetail", 1).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id", "order", "level", "status", "type",
					}).AddRow(1, "Loan Account Number", "lending.termLoanPartner.activeLoanList.loanDetail", nil, 1, 1, 1, "sectionCustom"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRole)).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order",
					}).
						AddRow(2, "Loan Onboarding Data", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData", "sectionCustom", 1, 1).
						AddRow(3, "Loan Overview", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview", "sectionCustom", 1, 2))

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
			})
		})

		When("Inactive Loan Account Number Information", func() {
			It("should successfully return loan account number data", func() {
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				Expect(err).ShouldNot(HaveOccurred())

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
					"identifierType": "SAFE_ID",
					"key": "lending.termLoanPartner.inactiveLoanList.loanDetail",
					"payload": {
						"loanAccountNumber": "1"
					}
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
						AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("lending.termLoanPartner.inactiveLoanList.loanDetail", 1).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id", "order", "level", "status", "type",
					}).AddRow(1, "Loan Account Number", "lending.termLoanPartner.inactiveLoanList.loanDetail", nil, 1, 1, 1, "sectionCustom"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRole)).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order",
					}).
						AddRow(2, "Loan Onboarding Data", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData", "sectionCustom", 1, 1).
						AddRow(3, "Loan Overview", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview", "sectionCustom", 1, 2))

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
			})
		})

		When("Active Loan Onboarding Data", func() {
			It("should successfully return loan onboarding data sections", func() {
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				Expect(err).ShouldNot(HaveOccurred())

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
					"identifierType": "SAFE_ID",
					"key": "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData",
					"payload": {
						"loanAccountNumber": "1"
					}
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
						AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData", 1).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id", "order", "level", "status", "type",
					}).AddRow(10, "Loan Onboarding Data", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData", nil, 1, 1, 1, "sectionCustom"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRole)).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order",
					}).
						AddRow(11, "Customer Information", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.customerInformation", "section", 10, 1).
						AddRow(12, "Address Detail", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.addressDetail", "section", 10, 2).
						AddRow(13, "Emergency Contact", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.emergencyContact", "section", 10, 3).
						AddRow(14, "Work Information", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.workInformation", "section", 10, 4).
						AddRow(15, "Contract List", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.contractList", "section", 10, 5).
						AddRow(16, "Drawdown Account Info", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.drawdownAccountInfo", "section", 10, 6).
						AddRow(17, "Device Information", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.deviceInformation", "section", 10, 7))

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
			})
		})

		When("Inactive Loan Onboarding Data", func() {
			It("should successfully return loan onboarding data sections", func() {
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				Expect(err).ShouldNot(HaveOccurred())

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
					"identifierType": "SAFE_ID",
					"key": "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData",
					"payload": {
						"loanAccountNumber": "1"
					}
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
						AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData", 1).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id", "order", "level", "status", "type",
					}).AddRow(10, "Loan Onboarding Data", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData", nil, 1, 1, 1, "sectionCustom"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRole)).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order",
					}).
						AddRow(11, "Customer Information", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.customerInformation", "section", 10, 1).
						AddRow(12, "Address Detail", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.addressDetail", "section", 10, 2).
						AddRow(13, "Emergency Contact", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.emergencyContact", "section", 10, 3).
						AddRow(14, "Work Information", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.workInformation", "section", 10, 4).
						AddRow(15, "Contract List", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.contractList", "section", 10, 5).
						AddRow(16, "Drawdown Account Info", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.drawdownAccountInfo", "section", 10, 6).
						AddRow(17, "Device Information", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.deviceInformation", "section", 10, 7))

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
			})
		})

		When("Active Loan Customer Information", func() {
			It("should successfully return customer information data", func() {
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				Expect(err).ShouldNot(HaveOccurred())

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
					"identifierType": "SAFE_ID",
					"key": "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.customerInformation",
					"payload": {
						"loanAccountNumber": "1"
					}
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
						AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.customerInformation", 1).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id", "order", "level", "status", "type",
					}).
						AddRow(1, "Customer Information", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.customerInformation", nil, 1, 1, 1, "section"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRole)).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order",
					}).
						AddRow(1, "Akulaku Customer ID", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.customerInformation.akulakuCustomerID", "oneliner", 1, 1).
						AddRow(2, "Superbank Customer ID", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.customerInformation.superbankCustomerID", "oneliner", 1, 2).
						AddRow(3, "Customer Name", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.customerInformation.customerName", "oneliner", 1, 3).
						AddRow(4, "KTP Number", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.customerInformation.ktpNumber", "oneliner", 1, 4).
						AddRow(5, "Phone Number", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.customerInformation.phoneNumber", "oneliner", 1, 5).
						AddRow(6, "Email Address", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.customerInformation.email", "oneliner", 1, 6).
						AddRow(7, "Nationality", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.customerInformation.nationality", "oneliner", 1, 7).
						AddRow(8, "Marital Status", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.customerInformation.maritalStatus", "oneliner", 1, 8).
						AddRow(9, "Gender", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.customerInformation.gender", "oneliner", 1, 9).
						AddRow(10, "User Selfie", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.customerInformation.selfieImage", "image", 1, 10).
						AddRow(11, "Mother Maiden Name", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.customerInformation.motherMaidenName", "oneliner", 1, 11).
						AddRow(12, "NPWP Availability", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.customerInformation.npwpAvailability", "oneliner", 1, 12).
						AddRow(13, "Risk Level", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.customerInformation.riskLevel", "oneliner", 1, 13).
						AddRow(14, "KTP File", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.customerInformation.ktpFile", "image", 1, 14).
						AddRow(15, "Onboarding Application ID", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.customerInformation.onboardingApplicationID", "oneliner", 1, 15).
						AddRow(16, "Partner Selfie", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.customerInformation.partnerSelfieImage", "image", 1, 16).
						AddRow(17, "Place Of Birth", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.customerInformation.placeOfBirth", "oneliner", 1, 17).
						AddRow(18, "Date Of Birth", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.customerInformation.dateOfBirth", "oneliner", 1, 18).
						AddRow(19, "Education", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.customerInformation.education", "oneliner", 1, 19).
						AddRow(20, "User Level", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.customerInformation.userLevel", "oneliner", 1, 20).
						AddRow(21, "User Type", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.customerInformation.userType", "oneliner", 1, 21).
						AddRow(22, "Onboarding Selfie", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.customerInformation.onboardingImage", "image", 1, 22))

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
			})
		})

		When("Inactive Loan Customer Information", func() {
			It("should successfully return customer information data", func() {
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				Expect(err).ShouldNot(HaveOccurred())

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
					"identifierType": "SAFE_ID",
					"key": "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.customerInformation",
					"payload": {
						"loanAccountNumber": "1"
					}
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
						AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.customerInformation", 1).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id", "order", "level", "status", "type",
					}).
						AddRow(1, "Customer Information", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.customerInformation", nil, 1, 1, 1, "section"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRole)).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order",
					}).
						AddRow(1, "Akulaku Customer ID", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.customerInformation.akulakuCustomerID", "oneliner", 1, 1).
						AddRow(2, "Superbank Customer ID", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.customerInformation.superbankCustomerID", "oneliner", 1, 2).
						AddRow(3, "Customer Name", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.customerInformation.customerName", "oneliner", 1, 3).
						AddRow(4, "KTP Number", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.customerInformation.ktpNumber", "oneliner", 1, 4).
						AddRow(5, "Phone Number", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.customerInformation.phoneNumber", "oneliner", 1, 5).
						AddRow(6, "Email Address", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.customerInformation.email", "oneliner", 1, 6).
						AddRow(7, "Nationality", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.customerInformation.nationality", "oneliner", 1, 7).
						AddRow(8, "Marital Status", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.customerInformation.maritalStatus", "oneliner", 1, 8).
						AddRow(9, "Gender", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.customerInformation.gender", "oneliner", 1, 9).
						AddRow(10, "User Selfie", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.customerInformation.selfieImage", "image", 1, 10).
						AddRow(11, "Mother Maiden Name", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.customerInformation.motherMaidenName", "oneliner", 1, 11).
						AddRow(12, "NPWP Availability", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.customerInformation.npwpAvailability", "oneliner", 1, 12).
						AddRow(13, "Risk Level", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.customerInformation.riskLevel", "oneliner", 1, 13).
						AddRow(14, "KTP File", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.customerInformation.ktpFile", "image", 1, 14).
						AddRow(15, "Onboarding Application ID", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.customerInformation.onboardingApplicationID", "oneliner", 1, 15).
						AddRow(16, "Partner Selfie", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.customerInformation.partnerSelfieImage", "image", 1, 16).
						AddRow(17, "Place Of Birth", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.customerInformation.placeOfBirth", "oneliner", 1, 17).
						AddRow(18, "Date Of Birth", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.customerInformation.dateOfBirth", "oneliner", 1, 18).
						AddRow(19, "Education", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.customerInformation.education", "oneliner", 1, 19).
						AddRow(20, "User Level", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.customerInformation.userLevel", "oneliner", 1, 20).
						AddRow(21, "User Type", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.customerInformation.userType", "oneliner", 1, 21).
						AddRow(22, "Onboarding Selfie", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.customerInformation.onboardingImage", "image", 1, 22))

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
			})
		})

		When("Active Loan Address Detail", func() {
			It("should successfully return address detail structure and data", func() {
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				Expect(err).ShouldNot(HaveOccurred())

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
					"identifierType": "SAFE_ID",
					"key": "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.addressDetail",
					"payload": {
						"loanAccountNumber": "1"
					}
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.addressDetail", 1).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id", "order", "level", "status", "type",
					}).AddRow(1, "Address Detail", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.addressDetail", nil, 1, 1, 1, "section"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRole)).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order",
					}).
						AddRow(1, "KTP Address", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.addressDetail.ktpAddress", "oneliner", 1, 1).
						AddRow(2, "Postal Code", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.addressDetail.ktpPostalCode", "oneliner", 1, 2).
						AddRow(3, "Province", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.addressDetail.ktpProvince", "oneliner", 1, 3).
						AddRow(4, "City", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.addressDetail.ktpCity", "oneliner", 1, 4).
						AddRow(5, "District", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.addressDetail.ktpDistrict", "oneliner", 1, 5).
						AddRow(6, "Sub District", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.addressDetail.ktpSubDistrict", "oneliner", 1, 6).
						AddRow(7, "Country", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.addressDetail.ktpCountry", "oneliner", 1, 7).
						AddRow(8, "Domicile Address", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.addressDetail.domicileAddress", "oneliner", 1, 8).
						AddRow(9, "Postal Code", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.addressDetail.domicilePostalCode", "oneliner", 1, 9).
						AddRow(10, "Province", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.addressDetail.domicileProvince", "oneliner", 1, 10).
						AddRow(11, "City", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.addressDetail.domicileCity", "oneliner", 1, 11).
						AddRow(12, "District", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.addressDetail.domicileDistrict", "oneliner", 1, 12).
						AddRow(13, "Sub District", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.addressDetail.domicileSubDistrict", "oneliner", 1, 13).
						AddRow(14, "Country", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.addressDetail.domicileCountry", "oneliner", 1, 14))

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
			})
		})

		When("Inactive Loan Address Detail", func() {
			It("should successfully return address detail structure and data", func() {
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				Expect(err).ShouldNot(HaveOccurred())

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
					"identifierType": "SAFE_ID",
					"key": "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.addressDetail",
					"payload": {
						"loanAccountNumber": "1"
					}
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.addressDetail", 1).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id", "order", "level", "status", "type",
					}).AddRow(1, "Address Detail", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.addressDetail", nil, 1, 1, 1, "section"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRole)).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order",
					}).
						AddRow(1, "KTP Address", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.addressDetail.ktpAddress", "oneliner", 1, 1).
						AddRow(2, "Postal Code", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.addressDetail.ktpPostalCode", "oneliner", 1, 2).
						AddRow(3, "Province", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.addressDetail.ktpProvince", "oneliner", 1, 3).
						AddRow(4, "City", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.addressDetail.ktpCity", "oneliner", 1, 4).
						AddRow(5, "District", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.addressDetail.ktpDistrict", "oneliner", 1, 5).
						AddRow(6, "Sub District", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.addressDetail.ktpSubDistrict", "oneliner", 1, 6).
						AddRow(7, "Country", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.addressDetail.ktpCountry", "oneliner", 1, 7).
						AddRow(8, "Domicile Address", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.addressDetail.domicileAddress", "oneliner", 1, 8).
						AddRow(9, "Postal Code", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.addressDetail.domicilePostalCode", "oneliner", 1, 9).
						AddRow(10, "Province", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.addressDetail.domicileProvince", "oneliner", 1, 10).
						AddRow(11, "City", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.addressDetail.domicileCity", "oneliner", 1, 11).
						AddRow(12, "District", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.addressDetail.domicileDistrict", "oneliner", 1, 12).
						AddRow(13, "Sub District", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.addressDetail.domicileSubDistrict", "oneliner", 1, 13).
						AddRow(14, "Country", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.addressDetail.domicileCountry", "oneliner", 1, 14))

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
			})
		})

		When("Active Loan Emergency Contact", func() {
			It("should successfully return emergency contact structure and data", func() {
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				Expect(err).ShouldNot(HaveOccurred())

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
					"identifierType": "SAFE_ID",
					"key": "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.emergencyContact",
					"payload": {
						"loanAccountNumber": "1"
					}
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
						AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.emergencyContact", 1).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id", "order", "level", "status", "type",
					}).AddRow(1, "Emergency Contact", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.emergencyContact", nil, 3, 3, 1, "section"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRole)).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order",
					}).
						AddRow(1, "Emergency Contact Name", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.emergencyContact.emergencyContactName", "oneliner", 1, 1).
						AddRow(2, "Emergency Contact Phone Number", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.emergencyContact.emergencyContactPhoneNumber", "oneliner", 1, 2).
						AddRow(3, "Emergency Contact Relationship", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.emergencyContact.emergencyContactRelationship", "oneliner", 1, 3))

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
			})
		})

		When("Inactive Loan Emergency Contact", func() {
			It("should successfully return emergency contact structure and data", func() {
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				Expect(err).ShouldNot(HaveOccurred())

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
					"identifierType": "SAFE_ID",
					"key": "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.emergencyContact",
					"payload": {
						"loanAccountNumber": "1"
					}
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
						AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.emergencyContact", 1).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id", "order", "level", "status", "type",
					}).AddRow(1, "Emergency Contact", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.emergencyContact", nil, 3, 3, 1, "section"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRole)).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order",
					}).
						AddRow(1, "Emergency Contact Name", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.emergencyContact.emergencyContactName", "oneliner", 1, 1).
						AddRow(2, "Emergency Contact Phone Number", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.emergencyContact.emergencyContactPhoneNumber", "oneliner", 1, 2).
						AddRow(3, "Emergency Contact Relationship", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.emergencyContact.emergencyContactRelationship", "oneliner", 1, 3))

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
			})
		})

		When("Active Loan Work Information", func() {
			It("should successfully return work information structure and data", func() {
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				Expect(err).ShouldNot(HaveOccurred())

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
					"identifierType": "SAFE_ID",
					"key": "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.workInformation",
					"payload": {
						"loanAccountNumber": "1"
					}
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
						AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.workInformation", 1).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id", "order", "level", "status", "type",
					}).AddRow(1, "Work Information", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.workInformation", nil, 4, 3, 1, "section"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRole)).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order",
					}).
						AddRow(1, "Working Months", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.workInformation.workingMonths", "oneliner", 1, 1).
						AddRow(2, "Stay Months", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.workInformation.stayMonths", "oneliner", 1, 2).
						AddRow(3, "Income Source", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.workInformation.incomeSource", "oneliner", 1, 3).
						AddRow(4, "Monthly Income", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.workInformation.monthlyIncome", "oneliner", 1, 4).
						AddRow(5, "Company Name", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.workInformation.companyName", "oneliner", 1, 5).
						AddRow(6, "Business Type", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.workInformation.businessType", "oneliner", 1, 6).
						AddRow(7, "Work Type", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.workInformation.workType", "oneliner", 1, 7).
						AddRow(8, "Job Position", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.workInformation.jobPosition", "oneliner", 1, 8))

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
			})
		})

		When("Inactive Loan Work Information", func() {
			It("should successfully return work information structure and data", func() {
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				Expect(err).ShouldNot(HaveOccurred())

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
					"identifierType": "SAFE_ID",
					"key": "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.workInformation",
					"payload": {
						"loanAccountNumber": "1"
					}
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
						AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.workInformation", 1).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id", "order", "level", "status", "type",
					}).AddRow(1, "Work Information", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.workInformation", nil, 4, 3, 1, "section"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRole)).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order",
					}).
						AddRow(1, "Working Months", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.workInformation.workingMonths", "oneliner", 1, 1).
						AddRow(2, "Stay Months", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.workInformation.stayMonths", "oneliner", 1, 2).
						AddRow(3, "Income Source", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.workInformation.incomeSource", "oneliner", 1, 3).
						AddRow(4, "Monthly Income", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.workInformation.monthlyIncome", "oneliner", 1, 4).
						AddRow(5, "Company Name", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.workInformation.companyName", "oneliner", 1, 5).
						AddRow(6, "Business Type", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.workInformation.businessType", "oneliner", 1, 6).
						AddRow(7, "Work Type", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.workInformation.workType", "oneliner", 1, 7).
						AddRow(8, "Job Position", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.workInformation.jobPosition", "oneliner", 1, 8))

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
			})
		})

		When("Active Loan Contract List", func() {
			It("should successfully return contract list structure and data", func() {
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				Expect(err).ShouldNot(HaveOccurred())

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
					"identifierType": "SAFE_ID",
					"key": "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.contractList",
					"payload": {
						"loanAccountNumber": "1"
					}
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
						AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.contractList", 1).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id", "order", "level", "status", "type",
					}).AddRow(1, "Contract List", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.contractList", nil, 5, 3, 1, "section"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRole)).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order",
					}).
						AddRow(1, "Contract Name", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.contractList.contractName", "oneliner", 1, 1).
						AddRow(2, "Contract URL", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.contractList.contractUrl", "oneliner", 1, 2).
						AddRow(3, "Timestamp", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.contractList.contractTimestamp", "oneliner", 1, 3))

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
			})
		})

		When("Inactive Loan Contract List", func() {
			It("should successfully return contract list structure and data", func() {
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				Expect(err).ShouldNot(HaveOccurred())

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
					"identifierType": "SAFE_ID",
					"key": "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.contractList",
					"payload": {
						"loanAccountNumber": "1"
					}
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
						AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.contractList", 1).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id", "order", "level", "status", "type",
					}).AddRow(1, "Contract List", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.contractList", nil, 5, 3, 1, "section"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRole)).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order",
					}).
						AddRow(1, "Contract Name", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.contractList.contractName", "oneliner", 1, 1).
						AddRow(2, "Contract URL", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.contractList.contractUrl", "oneliner", 1, 2).
						AddRow(3, "Timestamp", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.contractList.contractTimestamp", "oneliner", 1, 3))

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
			})
		})

		When("Active Loan Drawdown Account Info", func() {
			It("should successfully return drawdown account info structure and data", func() {
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				Expect(err).ShouldNot(HaveOccurred())

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": null,
					"identifierType": "SAFE_ID",
					"key": "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.drawdownAccountInfo",
					"payload": {
						"loanAccountNumber": "1"
					}
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
						AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.drawdownAccountInfo", 1).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id", "order", "level", "status", "type",
					}).AddRow(1, "Drawdown Account Info", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.drawdownAccountInfo", nil, 1, 3, 1, "section"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRole)).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order",
					}).
						AddRow(1, "Drawdown Amount", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.drawdownAccountInfo.drawdownAmount", "oneliner", 1, 1).
						AddRow(2, "Bank Name", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.drawdownAccountInfo.drawdownBankName", "oneliner", 1, 2).
						AddRow(3, "Drawdown Account Name", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.drawdownAccountInfo.drawdownAccountName", "oneliner", 1, 3).
						AddRow(4, "Bank Code", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.drawdownAccountInfo.drawdownBankCode", "oneliner", 1, 4))

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
			})
		})

		When("Inactive Loan Drawdown Account Info", func() {
			It("should successfully return drawdown account info structure and data", func() {
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				Expect(err).ShouldNot(HaveOccurred())

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": null,
					"identifierType": "SAFE_ID",
					"key": "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.drawdownAccountInfo",
					"payload": {
						"loanAccountNumber": "1"
					}
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
						AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.drawdownAccountInfo", 1).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id", "order", "level", "status", "type",
					}).AddRow(1, "Drawdown Account Info", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.drawdownAccountInfo", nil, 1, 3, 1, "section"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRole)).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order",
					}).
						AddRow(1, "Drawdown Amount", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.drawdownAccountInfo.drawdownAmount", "oneliner", 1, 1).
						AddRow(2, "Bank Name", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.drawdownAccountInfo.drawdownBankName", "oneliner", 1, 2).
						AddRow(3, "Drawdown Account Name", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.drawdownAccountInfo.drawdownAccountName", "oneliner", 1, 3).
						AddRow(4, "Bank Code", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.drawdownAccountInfo.drawdownBankCode", "oneliner", 1, 4))

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
			})
		})

		When("Active Loan Device Information", func() {
			It("should successfully return device information structure and data", func() {
				// Generate JWT token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				Expect(err).ShouldNot(HaveOccurred())

				// Header dan body
				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
					"identifierType": "SAFE_ID",
					"key": "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.deviceInformation",
					"payload": {
						"loanAccountNumber": "1"
					}
				}`)

				// Mocks
				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.deviceInformation", 1).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id", "order", "level", "status", "type",
					}).AddRow(1, "Device Information", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.deviceInformation", nil, 1, 3, 1, "section"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRole)).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order",
					}).
						AddRow(1, "Device ID", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.deviceInformation.deviceID", "oneliner", 1, 1).
						AddRow(2, "Adjust ID", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.deviceInformation.adjustID", "oneliner", 1, 2).
						AddRow(3, "IMEI", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.deviceInformation.imei", "oneliner", 1, 3).
						AddRow(4, "IP Address", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.deviceInformation.ipAddress", "oneliner", 1, 4).
						AddRow(5, "Latitude", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.deviceInformation.latitude", "oneliner", 1, 5).
						AddRow(6, "Longitude", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.deviceInformation.longitude", "oneliner", 1, 6).
						AddRow(7, "App Package Name", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.deviceInformation.appPackageName", "oneliner", 1, 7).
						AddRow(8, "App Version", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.deviceInformation.appVersion", "oneliner", 1, 8).
						AddRow(9, "Device Brand", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.deviceInformation.deviceBrand", "oneliner", 1, 9).
						AddRow(10, "Device Model", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.deviceInformation.deviceModel", "oneliner", 1, 10).
						AddRow(11, "OS System", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.deviceInformation.osSystem", "oneliner", 1, 11).
						AddRow(12, "OS Version", "lending.termLoanPartner.activeLoanList.loanDetail.loanOnboardingData.deviceInformation.osVersion", "oneliner", 1, 12))

				// Post request
				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
			})
		})

		When("Inactive Loan Device Information", func() {
			It("should successfully return device information structure and data", func() {
				// Generate JWT token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				Expect(err).ShouldNot(HaveOccurred())

				// Header dan body
				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
					"identifierType": "SAFE_ID",
					"key": "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.deviceInformation",
					"payload": {
						"loanAccountNumber": "1"
					}
				}`)

				// Mocks
				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.deviceInformation", 1).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id", "order", "level", "status", "type",
					}).AddRow(1, "Device Information", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.deviceInformation", nil, 1, 3, 1, "section"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRole)).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order",
					}).
						AddRow(1, "Device ID", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.deviceInformation.deviceID", "oneliner", 1, 1).
						AddRow(2, "Adjust ID", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.deviceInformation.adjustID", "oneliner", 1, 2).
						AddRow(3, "IMEI", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.deviceInformation.imei", "oneliner", 1, 3).
						AddRow(4, "IP Address", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.deviceInformation.ipAddress", "oneliner", 1, 4).
						AddRow(5, "Latitude", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.deviceInformation.latitude", "oneliner", 1, 5).
						AddRow(6, "Longitude", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.deviceInformation.longitude", "oneliner", 1, 6).
						AddRow(7, "App Package Name", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.deviceInformation.appPackageName", "oneliner", 1, 7).
						AddRow(8, "App Version", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.deviceInformation.appVersion", "oneliner", 1, 8).
						AddRow(9, "Device Brand", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.deviceInformation.deviceBrand", "oneliner", 1, 9).
						AddRow(10, "Device Model", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.deviceInformation.deviceModel", "oneliner", 1, 10).
						AddRow(11, "OS System", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.deviceInformation.osSystem", "oneliner", 1, 11).
						AddRow(12, "OS Version", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOnboardingData.deviceInformation.osVersion", "oneliner", 1, 12))

				// Post request
				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
			})
		})

		When("Active Loan Overview", func() {
			It("should successfully return loan overview sections and tables", func() {
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				Expect(err).ShouldNot(HaveOccurred())

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
					"identifierType": "SAFE_ID",
					"key": "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview",
					"payload": {
						"loanAccountNumber": "1"
					}
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
						AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("lending.termLoanPartner.activeLoanList.loanDetail.loanOverview", 1).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id", "order", "level", "status", "type",
					}).AddRow(20, "Loan Overview", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview", nil, 1, 1, 1, "sectionCustom"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRole)).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order",
					}).
						AddRow(21, "Loan Summary", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.loanSummary", "section", 20, 1).
						AddRow(22, "Installment Details", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.installmentDetail", "table", 20, 2).
						AddRow(23, "Repayment Details", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.repaymentDetail", "table", 20, 3).
						AddRow(24, "VA Generation History", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.vaGenerationHistory", "table", 20, 4).
						AddRow(25, "Credit Information", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.creditInformation", "section", 20, 5))

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
			})
		})

		When("Inactive Loan Overview", func() {
			It("should successfully return loan overview sections and tables", func() {
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				Expect(err).ShouldNot(HaveOccurred())

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
					"identifierType": "SAFE_ID",
					"key": "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview",
					"payload": {
						"loanAccountNumber": "1"
					}
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
						AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview", 1).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id", "order", "level", "status", "type",
					}).AddRow(20, "Loan Overview", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview", nil, 1, 1, 1, "sectionCustom"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRole)).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order",
					}).
						AddRow(21, "Loan Summary", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.loanSummary", "section", 20, 1).
						AddRow(22, "Installment Details", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.installmentDetail", "table", 20, 2).
						AddRow(23, "Repayment Details", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.repaymentDetail", "table", 20, 3).
						AddRow(24, "VA Generation History", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.vaGenerationHistory", "table", 20, 4).
						AddRow(25, "Credit Information", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.creditInformation", "section", 20, 5))

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
			})
		})

		When("Active Loan Loan Summary", func() {
			It("should successfully return loan summary structure and data", func() {
				// Generate JWT Token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				Expect(err).ShouldNot(HaveOccurred())

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
					"identifierType": "SAFE_ID",
					"key": "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.loanSummary",
					"payload": {
						"loanAccountNumber": "1"
					}
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.loanSummary", 1).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id", "order", "level", "status", "type",
					}).AddRow(1, "Loan Summary", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.loanSummary", nil, 1, 1, 1, "section"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRole)).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order",
					}).
						AddRow(1, "Product ID", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.loanSummary.loanProductID", "oneliner", 1, 1).
						AddRow(2, "Superbank Loan ID", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.loanSummary.loanSuperbankLoanID", "oneliner", 1, 2).
						AddRow(3, "Superbank Loan Account No.", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.loanSummary.loanSuperbankLoanAccountNumber", "oneliner", 1, 3).
						AddRow(4, "Loan Application Status", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.loanSummary.loanApplicationStatus", "oneliner", 1, 4).
						AddRow(5, "Partner Name", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.loanSummary.loanAccountStatus", "oneliner", 1, 5).
						AddRow(6, "Akulaku Loan ID", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.loanSummary.loanPartnerName", "oneliner", 1, 6).
						AddRow(7, "Loan Amount", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.loanSummary.loanAkulakuLoanID", "oneliner", 1, 7).
						AddRow(8, "Commission", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.loanSummary.loanCommission", "oneliner", 1, 8).
						AddRow(9, "Tenor", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.loanSummary.loanTenor", "oneliner", 1, 9).
						AddRow(10, "Interest Rate", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.loanSummary.loanInterestRate", "oneliner", 1, 10).
						AddRow(11, "Interest Amount", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.loanSummary.loanInterestAmount", "oneliner", 1, 11).
						AddRow(12, "Loan Application Date", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.loanSummary.loanApplicationDate", "datetime", 1, 12).
						AddRow(13, "Disbursement Date", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.loanSummary.loanDisbursementDate", "datetime", 1, 13).
						AddRow(14, "First Due Date", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.loanSummary.loanFirstDueDate", "datetime", 1, 14).
						AddRow(15, "Next Due Date", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.loanSummary.loanNextDueDate", "datetime", 1, 15).
						AddRow(16, "Purpose of Credit", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.loanSummary.loanPurposeOfCredit", "oneliner", 1, 16).
						AddRow(17, "Maturity Date", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.loanSummary.loanMaturityDate", "datetime", 1, 17).
						AddRow(18, "Collectability", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.loanSummary.loanCollectability", "oneliner", 1, 18).
						AddRow(19, "DPD", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.loanSummary.loanDpd", "oneliner", 1, 19).
						AddRow(20, "Remaining Outstanding", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.loanSummary.loanRemainingOutstanding", "oneliner", 1, 20).
						AddRow(21, "Next Installment", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.loanSummary.loanNextInstallment", "oneliner", 1, 21).
						AddRow(22, "Loan Account Sub Status", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.loanSummary.loanAccountSubStatus", "oneliner", 1, 22).
						AddRow(23, "Code", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.loanSummary.loanCode", "oneliner", 1, 23).
						AddRow(24, "Reason", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.loanSummary.loanReason", "oneliner", 1, 24))

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
			})
		})

		When("Inactive Loan Loan Summary", func() {
			It("should successfully return loan summary structure and data", func() {
				// Generate JWT Token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				Expect(err).ShouldNot(HaveOccurred())

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
					"identifierType": "SAFE_ID",
					"key": "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.loanSummary",
					"payload": {
						"loanAccountNumber": "1"
					}
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.loanSummary", 1).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id", "order", "level", "status", "type",
					}).AddRow(1, "Loan Summary", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.loanSummary", nil, 1, 1, 1, "section"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRole)).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order",
					}).
						AddRow(1, "Product ID", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.loanSummary.loanProductID", "oneliner", 1, 1).
						AddRow(2, "Superbank Loan ID", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.loanSummary.loanSuperbankLoanID", "oneliner", 1, 2).
						AddRow(3, "Superbank Loan Account No.", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.loanSummary.loanSuperbankLoanAccountNumber", "oneliner", 1, 3).
						AddRow(4, "Loan Application Status", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.loanSummary.loanApplicationStatus", "oneliner", 1, 4).
						AddRow(5, "Partner Name", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.loanSummary.loanAccountStatus", "oneliner", 1, 5).
						AddRow(6, "Akulaku Loan ID", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.loanSummary.loanPartnerName", "oneliner", 1, 6).
						AddRow(7, "Loan Amount", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.loanSummary.loanAkulakuLoanID", "oneliner", 1, 7).
						AddRow(8, "Commission", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.loanSummary.loanCommission", "oneliner", 1, 8).
						AddRow(9, "Tenor", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.loanSummary.loanTenor", "oneliner", 1, 9).
						AddRow(10, "Interest Rate", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.loanSummary.loanInterestRate", "oneliner", 1, 10).
						AddRow(11, "Interest Amount", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.loanSummary.loanInterestAmount", "oneliner", 1, 11).
						AddRow(12, "Loan Application Date", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.loanSummary.loanApplicationDate", "datetime", 1, 12).
						AddRow(13, "Disbursement Date", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.loanSummary.loanDisbursementDate", "datetime", 1, 13).
						AddRow(14, "First Due Date", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.loanSummary.loanFirstDueDate", "datetime", 1, 14).
						AddRow(15, "Next Due Date", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.loanSummary.loanNextDueDate", "datetime", 1, 15).
						AddRow(16, "Purpose of Credit", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.loanSummary.loanPurposeOfCredit", "oneliner", 1, 16).
						AddRow(17, "Maturity Date", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.loanSummary.loanMaturityDate", "datetime", 1, 17).
						AddRow(18, "Collectability", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.loanSummary.loanCollectability", "oneliner", 1, 18).
						AddRow(19, "DPD", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.loanSummary.loanDpd", "oneliner", 1, 19).
						AddRow(20, "Remaining Outstanding", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.loanSummary.loanRemainingOutstanding", "oneliner", 1, 20).
						AddRow(21, "Next Installment", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.loanSummary.loanNextInstallment", "oneliner", 1, 21).
						AddRow(22, "Loan Account Sub Status", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.loanSummary.loanAccountSubStatus", "oneliner", 1, 22).
						AddRow(23, "Code", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.loanSummary.loanCode", "oneliner", 1, 23).
						AddRow(24, "Reason", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.loanSummary.loanReason", "oneliner", 1, 24))

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
			})
		})

		When("Active Loan Installment Detail", func() {
			It("should successfully return installment detail table structure and data", func() {
				// Generate JWT Token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				Expect(err).ShouldNot(HaveOccurred())

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
					"identifierType": "SAFE_ID",
					"key": "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.installmentDetail",
					"payload": {
						"loanAccountNumber": "1"
					}
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.installmentDetail", 1).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id", "order", "level", "status", "type",
					}).AddRow(1, "Installment Details", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.installmentDetail", nil, 1, 1, 1, "table"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRole)).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order",
					}).
						AddRow(1, "Installment ID", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentID", "oneliner", 1, 1).
						AddRow(2, "Installment Number", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentNumber", "oneliner", 1, 2).
						AddRow(3, "Due Date", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.installmentDetail.loanDueDate", "datetime", 1, 3).
						AddRow(4, "Total Outstanding", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.installmentDetail.loanTotalOutstanding", "oneliner", 1, 4).
						AddRow(5, "Principal Outstanding", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.installmentDetail.loanPrincipalOutstanding", "oneliner", 1, 5).
						AddRow(6, "Interest Outstanding", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.installmentDetail.loanInterestOutstanding", "oneliner", 1, 6).
						AddRow(7, "Late Fee Outstanding", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.installmentDetail.loanLateFeeOutstanding", "oneliner", 1, 7).
						AddRow(8, "Installment DPD", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentDpd", "oneliner", 1, 8).
						AddRow(9, "Update At", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.installmentDetail.loanUpdatedAt", "oneliner", 1, 9).
						AddRow(10, "Status", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentStatus", "oneliner", 1, 10).
						AddRow(11, "Detail", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentDetail", "sectionDetail", 1, 11))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRole)).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order",
					}).
						AddRow(1, "Installment ID", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentDetail.loanInstallmentID", "oneliner", 11, 1).
						AddRow(2, "Update At", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentDetail.loanUpdatedAt", "datetime", 11, 2).
						AddRow(3, "Insurance Amount", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentDetail.loanInsuranceAmount", "oneliner", 11, 3).
						AddRow(4, "Due Date", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentDetail.loanDueDate", "datetime", 11, 4).
						AddRow(5, "Principal Outstanding", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentDetail.loanPrincipalInstallment", "oneliner", 11, 5).
						AddRow(6, "Total Installment Outstanding", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentDetail.loanTotalInstallmentAmount", "oneliner", 11, 6).
						AddRow(7, "Installment DPD", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentDetail.loanInstallmentDpd", "oneliner", 11, 7).
						AddRow(8, "Installment Interest", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentDetail.loanInstallmentInterest", "oneliner", 11, 8).
						AddRow(9, "Status", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentDetail.loanInstallmentStatus", "oneliner", 11, 9).
						AddRow(10, "Total Outstanding", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentDetail.loanTotalOutstanding", "oneliner", 11, 10).
						AddRow(11, "Outstanding Insurance", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentDetail.loanOutstandingInsurance", "oneliner", 11, 11).
						AddRow(12, "Principal Outstanding", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentDetail.loanPrincipalOutstanding", "oneliner", 11, 12).
						AddRow(13, "Late Fee Outstanding", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentDetail.loanLateFeeOutstanding", "oneliner", 11, 13).
						AddRow(14, "Outstanding Interest", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentDetail.loanOutstandingInterest", "oneliner", 11, 14).
						AddRow(15, "Admin Fee Outstanding", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentDetail.loanAdminFeeOutstanding", "oneliner", 11, 15))

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
			})
		})

		When("Inactive Loan Installment Detail", func() {
			It("should successfully return installment detail table structure and data", func() {
				// Generate JWT Token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				Expect(err).ShouldNot(HaveOccurred())

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
					"identifierType": "SAFE_ID",
					"key": "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.installmentDetail",
					"payload": {
						"loanAccountNumber": "1"
					}
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.installmentDetail", 1).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id", "order", "level", "status", "type",
					}).AddRow(1, "Installment Details", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.installmentDetail", nil, 1, 1, 1, "table"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRole)).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order",
					}).
						AddRow(1, "Installment ID", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentID", "oneliner", 1, 1).
						AddRow(2, "Installment Number", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentNumber", "oneliner", 1, 2).
						AddRow(3, "Due Date", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.installmentDetail.loanDueDate", "datetime", 1, 3).
						AddRow(4, "Total Outstanding", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.installmentDetail.loanTotalOutstanding", "oneliner", 1, 4).
						AddRow(5, "Principal Outstanding", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.installmentDetail.loanPrincipalOutstanding", "oneliner", 1, 5).
						AddRow(6, "Interest Outstanding", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.installmentDetail.loanInterestOutstanding", "oneliner", 1, 6).
						AddRow(7, "Late Fee Outstanding", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.installmentDetail.loanLateFeeOutstanding", "oneliner", 1, 7).
						AddRow(8, "Installment DPD", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentDpd", "oneliner", 1, 8).
						AddRow(9, "Update At", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.installmentDetail.loanUpdatedAt", "oneliner", 1, 9).
						AddRow(10, "Status", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentStatus", "oneliner", 1, 10).
						AddRow(11, "Detail", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentDetail", "sectionDetail", 1, 11))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRole)).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order",
					}).
						AddRow(1, "Installment ID", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentDetail.loanInstallmentID", "oneliner", 11, 1).
						AddRow(2, "Update At", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentDetail.loanUpdatedAt", "datetime", 11, 2).
						AddRow(3, "Insurance Amount", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentDetail.loanInsuranceAmount", "oneliner", 11, 3).
						AddRow(4, "Due Date", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentDetail.loanDueDate", "datetime", 11, 4).
						AddRow(5, "Principal Outstanding", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentDetail.loanPrincipalInstallment", "oneliner", 11, 5).
						AddRow(6, "Total Installment Outstanding", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentDetail.loanTotalInstallmentAmount", "oneliner", 11, 6).
						AddRow(7, "Installment DPD", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentDetail.loanInstallmentDpd", "oneliner", 11, 7).
						AddRow(8, "Installment Interest", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentDetail.loanInstallmentInterest", "oneliner", 11, 8).
						AddRow(9, "Status", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentDetail.loanInstallmentStatus", "oneliner", 11, 9).
						AddRow(10, "Total Outstanding", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentDetail.loanTotalOutstanding", "oneliner", 11, 10).
						AddRow(11, "Outstanding Insurance", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentDetail.loanOutstandingInsurance", "oneliner", 11, 11).
						AddRow(12, "Principal Outstanding", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentDetail.loanPrincipalOutstanding", "oneliner", 11, 12).
						AddRow(13, "Late Fee Outstanding", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentDetail.loanLateFeeOutstanding", "oneliner", 11, 13).
						AddRow(14, "Outstanding Interest", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentDetail.loanOutstandingInterest", "oneliner", 11, 14).
						AddRow(15, "Admin Fee Outstanding", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.installmentDetail.loanInstallmentDetail.loanAdminFeeOutstanding", "oneliner", 11, 15))

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
			})
		})

		When("Active Loan Repayment Detail", func() {
			It("should successfully return repayment detail table structure and data", func() {
				// Generate JWT Token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				Expect(err).ShouldNot(HaveOccurred())

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
					"identifierType": "SAFE_ID",
					"key": "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.repaymentDetail",
					"payload": {
						"loanAccountNumber": "1"
					}
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.repaymentDetail", 1).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id", "order", "level", "status", "type",
					}).AddRow(1, "Repayment Details", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.repaymentDetail", nil, 1, 1, 1, "table"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRole)).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order",
					}).
						AddRow(1, "Loan Repayment ID", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.repaymentDetail.loanRepaymentID", "oneliner", 1, 1).
						AddRow(2, "Repayment Date", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.repaymentDetail.loanRepaymentDate", "datetime", 1, 2).
						AddRow(3, "VA Number", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.repaymentDetail.loanVaNumber", "oneliner", 1, 3).
						AddRow(4, "VA Issuer Name", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.repaymentDetail.loanVaIssuerName", "oneliner", 1, 4).
						AddRow(5, "Total Amount", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.repaymentDetail.loanTotalAmount", "oneliner", 1, 5).
						AddRow(6, "Status", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.repaymentDetail.loanRepaymentStatus", "oneliner", 1, 6).
						AddRow(7, "VA Transaction ID (PG)", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.repaymentDetail.loanPgVaTransactionID", "oneliner", 1, 7).
						AddRow(8, "VA Transaction ID (Superbank)", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.repaymentDetail.loanSuperbankVaTransactionID", "oneliner", 1, 8).
						AddRow(9, "Detail", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.repaymentDetail.loanRepaymentDetail", "sectionDetail", 1, 9))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRole)).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order",
					}).
						AddRow(1, "Repayment Date", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.repaymentDetail.loanRepaymentDetail.repaymentDate", "datetime", 9, 1).
						AddRow(2, "Total Amount", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.repaymentDetail.loanRepaymentDetail.repaymentTotalAmount", "currency", 9, 2).
						AddRow(3, "VA Transaction ID (Superbank)", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.repaymentDetail.loanRepaymentDetail.repaymentSuperbankVaTransactionID", "oneliner", 9, 3).
						AddRow(4, "VA Number", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.repaymentDetail.loanRepaymentDetail.repaymentVaNumber", "oneliner", 9, 4).
						AddRow(5, "Status", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.repaymentDetail.loanRepaymentDetail.repaymentStatus", "oneliner", 9, 5).
						AddRow(6, "VA Issuer Name", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.repaymentDetail.loanRepaymentDetail.repaymentVaIssuerName", "oneliner", 9, 6).
						AddRow(7, "VA Transaction ID (PG)", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.repaymentDetail.loanRepaymentDetail.repaymentPgVaTransactionID", "oneliner", 9, 7).
						AddRow(8, "Installment List", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.repaymentDetail.loanRepaymentDetail.installmentList", "sectionDetailTable", 9, 8))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRole)).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order",
					}).
						AddRow(1, "Installment Number", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.repaymentDetail.loanRepaymentDetail.installmentList.repaymentInstallmentNumber", "oneliner", 8, 1).
						AddRow(2, "Amount", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.repaymentDetail.loanRepaymentDetail.installmentList.repaymentInstallmentAmount", "oneliner", 8, 2).
						AddRow(3, "Principal Paid", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.repaymentDetail.loanRepaymentDetail.installmentList.repaymentInstallmentPrincipalPaid", "oneliner", 8, 3).
						AddRow(4, "Interest Paid", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.repaymentDetail.loanRepaymentDetail.installmentList.repaymentInstallmentInterestPaid", "oneliner", 8, 4).
						AddRow(5, "Admin Fee Paid", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.repaymentDetail.loanRepaymentDetail.installmentList.repaymentInstallmentAdminFeePaid", "oneliner", 8, 5).
						AddRow(6, "Late Fee Paid", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.repaymentDetail.loanRepaymentDetail.installmentList.repaymentInstallmentLateFeePaid", "oneliner", 8, 6).
						AddRow(7, "Insurance Paid", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.repaymentDetail.loanRepaymentDetail.installmentList.repaymentInstallmentInsurancePaid", "oneliner", 8, 7))

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
			})
		})

		When("Inactive Loan Repayment Detail", func() {
			It("should successfully return repayment detail table structure and data", func() {
				// Generate JWT Token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				Expect(err).ShouldNot(HaveOccurred())

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
					"identifierType": "SAFE_ID",
					"key": "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.repaymentDetail",
					"payload": {
						"loanAccountNumber": "1"
					}
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.repaymentDetail", 1).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id", "order", "level", "status", "type",
					}).AddRow(1, "Repayment Details", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.repaymentDetail", nil, 1, 1, 1, "table"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRole)).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order",
					}).
						AddRow(1, "Loan Repayment ID", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.repaymentDetail.loanRepaymentID", "oneliner", 1, 1).
						AddRow(2, "Repayment Date", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.repaymentDetail.loanRepaymentDate", "datetime", 1, 2).
						AddRow(3, "VA Number", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.repaymentDetail.loanVaNumber", "oneliner", 1, 3).
						AddRow(4, "VA Issuer Name", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.repaymentDetail.loanVaIssuerName", "oneliner", 1, 4).
						AddRow(5, "Total Amount", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.repaymentDetail.loanTotalAmount", "oneliner", 1, 5).
						AddRow(6, "Status", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.repaymentDetail.loanRepaymentStatus", "oneliner", 1, 6).
						AddRow(7, "VA Transaction ID (PG)", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.repaymentDetail.loanPgVaTransactionID", "oneliner", 1, 7).
						AddRow(8, "VA Transaction ID (Superbank)", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.repaymentDetail.loanSuperbankVaTransactionID", "oneliner", 1, 8).
						AddRow(9, "Detail", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.repaymentDetail.loanRepaymentDetail", "sectionDetail", 1, 9))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRole)).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order",
					}).
						AddRow(1, "Repayment Date", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.repaymentDetail.loanRepaymentDetail.repaymentDate", "datetime", 9, 1).
						AddRow(2, "Total Amount", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.repaymentDetail.loanRepaymentDetail.repaymentTotalAmount", "currency", 9, 2).
						AddRow(3, "VA Transaction ID (Superbank)", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.repaymentDetail.loanRepaymentDetail.repaymentSuperbankVaTransactionID", "oneliner", 9, 3).
						AddRow(4, "VA Number", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.repaymentDetail.loanRepaymentDetail.repaymentVaNumber", "oneliner", 9, 4).
						AddRow(5, "Status", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.repaymentDetail.loanRepaymentDetail.repaymentStatus", "oneliner", 9, 5).
						AddRow(6, "VA Issuer Name", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.repaymentDetail.loanRepaymentDetail.repaymentVaIssuerName", "oneliner", 9, 6).
						AddRow(7, "VA Transaction ID (PG)", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.repaymentDetail.loanRepaymentDetail.repaymentPgVaTransactionID", "oneliner", 9, 7).
						AddRow(8, "Installment List", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.repaymentDetail.loanRepaymentDetail.installmentList", "sectionDetailTable", 9, 8))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRole)).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order",
					}).
						AddRow(1, "Installment Number", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.repaymentDetail.loanRepaymentDetail.installmentList.repaymentInstallmentNumber", "oneliner", 8, 1).
						AddRow(2, "Amount", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.repaymentDetail.loanRepaymentDetail.installmentList.repaymentInstallmentAmount", "oneliner", 8, 2).
						AddRow(3, "Principal Paid", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.repaymentDetail.loanRepaymentDetail.installmentList.repaymentInstallmentPrincipalPaid", "oneliner", 8, 3).
						AddRow(4, "Interest Paid", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.repaymentDetail.loanRepaymentDetail.installmentList.repaymentInstallmentInterestPaid", "oneliner", 8, 4).
						AddRow(5, "Admin Fee Paid", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.repaymentDetail.loanRepaymentDetail.installmentList.repaymentInstallmentAdminFeePaid", "oneliner", 8, 5).
						AddRow(6, "Late Fee Paid", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.repaymentDetail.loanRepaymentDetail.installmentList.repaymentInstallmentLateFeePaid", "oneliner", 8, 6).
						AddRow(7, "Insurance Paid", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.repaymentDetail.loanRepaymentDetail.installmentList.repaymentInstallmentInsurancePaid", "oneliner", 8, 7))

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
			})
		})

		When("Active VA Generation History", func() {
			It("should successfully return VA generation history table structure and data", func() {
				// Generate JWT Token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				Expect(err).ShouldNot(HaveOccurred())

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
					"identifierType": "SAFE_ID",
					"key": "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.vaGenerationHistory",
					"payload": {
						"loanAccountNumber": "1"
					}
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.vaGenerationHistory", 1).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id", "order", "level", "status", "type",
					}).AddRow(1, "VA Generation History", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.vaGenerationHistory", nil, 1, 1, 1, "table"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRole)).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order",
					}).
						AddRow(1, "VA Number", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.vaGenerationHistory.vaNumber", "oneliner", 1, 1).
						AddRow(2, "VA Issuer Name", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.vaGenerationHistory.vaIssuerName", "sectionCustom", 1, 2).
						AddRow(3, "VA Generation Timestamp", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.vaGenerationHistory.vaGenerationTimestamp", "datetime", 1, 3).
						AddRow(4, "VA Requested/TriggeredTimestamp", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.vaGenerationHistory.vaRequestTimestamp", "datetime", 1, 4).
						AddRow(5, "VA Expiry Date", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.vaGenerationHistory.vaExpiryDate", "oneliner", 1, 5).
						AddRow(6, "Status", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.vaGenerationHistory.vaStatus", "oneliner", 1, 6))

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
			})
		})

		When("Inactive VA Generation History", func() {
			It("should successfully return VA generation history table structure and data", func() {
				// Generate JWT Token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				Expect(err).ShouldNot(HaveOccurred())

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
					"identifierType": "SAFE_ID",
					"key": "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.vaGenerationHistory",
					"payload": {
						"loanAccountNumber": "1"
					}
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.vaGenerationHistory", 1).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id", "order", "level", "status", "type",
					}).AddRow(1, "VA Generation History", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.vaGenerationHistory", nil, 1, 1, 1, "table"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRole)).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order",
					}).
						AddRow(1, "VA Number", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.vaGenerationHistory.vaNumber", "oneliner", 1, 1).
						AddRow(2, "VA Issuer Name", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.vaGenerationHistory.vaIssuerName", "sectionCustom", 1, 2).
						AddRow(3, "VA Generation Timestamp", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.vaGenerationHistory.vaGenerationTimestamp", "datetime", 1, 3).
						AddRow(4, "VA Requested/TriggeredTimestamp", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.vaGenerationHistory.vaRequestTimestamp", "datetime", 1, 4).
						AddRow(5, "VA Expiry Date", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.vaGenerationHistory.vaExpiryDate", "oneliner", 1, 5).
						AddRow(6, "Status", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.vaGenerationHistory.vaStatus", "oneliner", 1, 6))

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
			})
		})

		When("Active Credit Information", func() {
			It("should successfully return Credit Information section structure and data", func() {
				// Generate JWT Token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				Expect(err).ShouldNot(HaveOccurred())

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
					"identifierType": "SAFE_ID",
					"key": "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.creditInformation",
					"payload": {
						"loanAccountNumber": "1"
					}
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.creditInformation", 1).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id", "order", "level", "status", "type",
					}).AddRow(1, "Credit Information", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.creditInformation", nil, 1, 1, 1, "section"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRole)).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order",
					}).
						AddRow(1, "User Level", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.creditInformation.creditUserLevel", "oneliner", 1, 1).
						AddRow(2, "User Type", "lending.termLoanPartner.activeLoanList.loanDetail.loanOverview.creditInformation.creditUserType", "oneliner", 1, 2))

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
			})
		})

		When("Inactive Credit Information", func() {
			It("should successfully return Credit Information section structure and data", func() {
				// Generate JWT Token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				Expect(err).ShouldNot(HaveOccurred())

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "e09e655a-4a6d-47b0-9078-ffec9cbf107f",
					"identifierType": "SAFE_ID",
					"key": "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.creditInformation",
					"payload": {
						"loanAccountNumber": "1"
					}
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.creditInformation", 1).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id", "order", "level", "status", "type",
					}).AddRow(1, "Credit Information", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.creditInformation", nil, 1, 1, 1, "section"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRole)).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order",
					}).
						AddRow(1, "User Level", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.creditInformation.creditUserLevel", "oneliner", 1, 1).
						AddRow(2, "User Type", "lending.termLoanPartner.inactiveLoanList.loanDetail.loanOverview.creditInformation.creditUserType", "oneliner", 1, 2))

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
			})
		})

	})
})
