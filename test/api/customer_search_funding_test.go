package api

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"regexp"

	txHistory "gitlab.myteksi.net/bersama/transaction-history/api"
	txHistoryMock "gitlab.myteksi.net/bersama/transaction-history/api/mock"
	PayAuthZApi "gitlab.myteksi.net/dakota/payment/pay-authz/api"
	payAuthzMock "gitlab.myteksi.net/dakota/payment/pay-authz/api/mock"
	transactionLimitAPI "gitlab.myteksi.net/dakota/transaction-limit/api"
	txLimitMock "gitlab.myteksi.net/dakota/transaction-limit/api/mock"
	customerJournalAPI "gitlab.super-id.net/bersama/corex/customer-journal/api"
	customerJournalMock "gitlab.super-id.net/bersama/corex/customer-journal/api/mock"
	auditTrailLogic "gitlab.super-id.net/bersama/opsce/onedash-be/module/audittrail/logic"
	permissionManagementLogic "gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/logic"

	"github.com/DATA-DOG/go-sqlmock"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
	accountService "gitlab.myteksi.net/bersama/core-banking/account-service/api"
	accountServiceMock "gitlab.myteksi.net/bersama/core-banking/account-service/api/mock"
	customerExperienceMock "gitlab.myteksi.net/bersama/customer-experience/api/mock"

	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage/mocks"
	customerMaster "gitlab.myteksi.net/bersama/customer-master/api/v2"
	customerMasterMock "gitlab.myteksi.net/bersama/customer-master/api/v2/mock"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/logic"
	"gitlab.super-id.net/bersama/opsce/onedash-be/test/resources"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils/jwt"
)

var _ = Describe("Customer Search", func() {
	var (
		db     *sql.DB
		mocker sqlmock.Sqlmock
	)

	BeforeEach(func() {
		var err error
		db, mocker, err = sqlmock.New()
		Expect(err).NotTo(HaveOccurred())

		mockCustomerMaster = &customerMasterMock.CustomerMaster{}
		mockCustomerExperience = &customerExperienceMock.CustomerExperience{}
		mockAccountService = &accountServiceMock.AccountService{}
		mockCustomerJournal = &customerJournalMock.CustomerJournal{}
		mockTxLimit = &txLimitMock.TransactionLimit{}
		mockPayAuthz = &payAuthzMock.PayAuthz{}
		mockTxHistory = &txHistoryMock.TxHistory{}

		config := &logic.MockProcessConfig{
			CustomerMaster:     mockCustomerMaster,
			CustomerExperience: mockCustomerExperience,
			AccountService:     mockAccountService,
			AppConfig:          service.AppConfig,
			TxLimit:            mockTxLimit,
			CustomerJournal:    mockCustomerJournal,
			PayAuthz:           mockPayAuthz,
			TransactionHistory: mockTxHistory,
		}
		logic.MockInitLogic(config)

		mockStorage = &mocks.MockDatabaseStore{}
		commonStorage.DBStoreD = mockStorage

		auditTrailLogic.MockInitLogic(service.AppConfig)
		permissionManagementLogic.MockInitLogic(service.AppConfig)

	})

	AfterEach(func() {
		db.Close()
	})

	Context("CustomerSearch", func() {
		When("user has valid permissions and getting funding tab", func() {
			It("should successfully return customer search results", func() {
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "c0575d1f-97a6-494d-8228-42711ae1fdea",
    				"identifierType": "SAFE_ID",
   					"key": "funding"
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
						AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("funding", 1). // keyword and status
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id",
						"order", "level", "status", "type",
					}).AddRow(
						1, "Funding", "funding", nil, 1, 1, 1, "tab",
					))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRole)).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order"}).
						AddRow(1, "List of Funding Account", "listOfFundingAccount", "dropdown", 1, 1).
						AddRow(2, "Transaction Configuration", "transactionConfiguration", "dropdown", 1, 2).
						AddRow(3, "External Linkages", "externalLinkages", "dropdown", 1, 3))

				var expectedResponseCustomerLeanInfo = `{
					"customer": {
						"data": {
							"ID": "c0575d1f-97a6-494d-8228-42711ae1fdea",
							"dateOfBirth": "1988-08-18",
							"gender": "MALE",
							"maritalStatus": "SINGLE",
							"motherMaidenName": "superbank",
							"name": "Efren Ruecker Sr",
							"nationality": "WNI",
							"placeOfBirth": "PEMALANG",
							"publicID": "ID179555577230",
							"relatedCounterPartyInd": false,
							"startDate": "2024-06-04T03:06:04Z",
							"status": "ONBOARDED",
							"type": "NAT_PERSON"
						}
					}
				}`
				var mockResponse *customerMaster.GetCustomerByIdentifierResponse
				_ = json.Unmarshal([]byte(expectedResponseCustomerLeanInfo), &mockResponse)

				// mock for GetCustomerLean
				mockCustomerMaster.On("GetCustomerLean", mock.Anything, &customerMaster.GetCustomerLeanRequest{
					Identifier:     "c0575d1f-97a6-494d-8228-42711ae1fdea",
					IdentifierType: customerMaster.IdentifierType_SAFE_ID,
					ServiceID:      constants.DIGIBANK,
					Data:           []customerMaster.DataFetchedType{customerMaster.Data_PersonalInfo},
				}).Return(mockResponse, nil).Once()

				expectedResponse := `{
					"status": "Success",
					"structure": {
						"key": "funding",
						"type": "tab",
						"label": "Funding",
						"children": [
							{
								"key": "listOfFundingAccount",
								"type": "dropdown",
								"label": "List of Funding Account"
							},
							{
								"key": "transactionConfiguration",
								"type": "dropdown",
								"label": "Transaction Configuration"
							},
							{
								"key": "externalLinkages",
								"type": "dropdown",
								"label": "External Linkages"
							}
						]
					},
					"data": {
						"cif": "ID179555577230",
						"meta": null,
						"safeID": "c0575d1f-97a6-494d-8228-42711ae1fdea"
					}
				}`

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
				Expect(response.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("user has valid permissions and getting list of funding account subtab", func() {
			It("should successfully return customer search results", func() {
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "c0575d1f-97a6-494d-8228-42711ae1fdea",
    				"identifierType": "SAFE_ID",
   					"key": "listOfFundingAccount"
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
						AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("listOfFundingAccount", 1). // keyword and status
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id",
						"order", "level", "status", "type",
					}).AddRow(
						1, "List of Funding Account", "listOfFundingAccount", nil, 1, 2, 1, "dropdown",
					))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRole)).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order"}).
						AddRow(1, "Main CASA & Saku Account", "mainCasaAndSakuAccount", "section", 1, 1).
						AddRow(2, "Piggybank", "piggybank", "section", 1, 2).
						AddRow(3, "Term Deposit", "termDeposit", "section", 1, 3))

				expectedResponse := `{
					"status": "Success",
					"structure": {
						"key": "listOfFundingAccount",
						"type": "dropdown",
						"label": "List of Funding Account",
						"children": [
							{
								"key": "mainCasaAndSakuAccount",
								"type": "section",
								"label": "Main CASA \u0026 Saku Account",
								"hasChildren": true
							},
							{
								"key": "piggybank",
								"type": "section",
								"label": "Piggybank",
								"hasChildren": true
							},
							{
								"key": "termDeposit",
								"type": "section",
								"label": "Term Deposit",
								"hasChildren": true
							}
						]
					},
					"data": {
						"cif": null,
						"meta": null,
						"safeID": "c0575d1f-97a6-494d-8228-42711ae1fdea"
					}
				}`

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
				Expect(response.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("user has valid permissions and getting main casa account", func() {
			It("should successfully return customer search results", func() {
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "c0575d1f-97a6-494d-8228-42711ae1fdea",
    				"identifierType": "SAFE_ID",
   					"key": "mainCasaAndSakuAccount"
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
						AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("mainCasaAndSakuAccount", 1). // keyword and status
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id",
						"order", "level", "status", "type",
					}).AddRow(
						1, "Main Casa & Saku Account", "mainCasaAndSakuAccount", nil, 1, 3, 1, "sectionArray",
					))

				productVariantIDs := constants.ProductVariantMappingBySubTab[constants.KeyMainCasaAndSakuAccount]

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRole)).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order"}).
						AddRow(1, "Account ID", "accountID", "oneliner", 1, 1).
						AddRow(2, "Account Type", "accountType", "oneliner", 1, 2).
						AddRow(3, "Account Name", "accountName", "oneliner", 1, 3))

				var mockResponseCust *customerMaster.GetCustomerByIdentifierResponse
				_ = json.Unmarshal([]byte(resources.ExpectedCustomerLeanResponse), &mockResponseCust)

				// mock for GetCustomerLean
				mockCustomerMaster.On("GetCustomerLean", mock.Anything, &customerMaster.GetCustomerLeanRequest{
					Identifier:     "c0575d1f-97a6-494d-8228-42711ae1fdea",
					IdentifierType: customerMaster.IdentifierType_SAFE_ID,
					ServiceID:      constants.DIGIBANK,
					Data:           []customerMaster.DataFetchedType{customerMaster.Data_PersonalInfo},
				}).Return(mockResponseCust, nil).Once()

				var mockResponse *accountService.ListCASAAccountsForCustomerDetailResponse
				_ = json.Unmarshal([]byte(resources.ExpectedResponseGetAccountListPiggybank), &mockResponse)

				// mock for get
				mockAccountService.On("ListCASAAccountsForCustomerDetail", mock.Anything, &accountService.ListCASAAccountsForCustomerDetailRequest{
					CifNumber:         "ID179555577230",
					FetchBalance:      true,
					FetchInterestRate: true,
					ProductVariantIDs: productVariantIDs,
				}).Return(mockResponse, nil).Once()

				expectedResponse := `{
					"status": "Success",
					"structure": {
					  "key": "mainCasaAndSakuAccount",
					  "type": "sectionArray",
					  "label": "Main Casa \u0026 Saku Account",
					  "children": [
						{
						  "key": "accountID",
						  "type": "oneliner",
						  "label": "Account ID"
						},
						{
						  "key": "accountType",
						  "type": "oneliner",
						  "label": "Account Type"
						},
						{
						  "key": "accountName",
						  "type": "oneliner",
						  "label": "Account Name"
						}
					  ]
					},
					"data": {
					  "cif": "ID179555577230",
					  "mainCasaAndSakuAccount": {
						"accounts": [
						  {
							"accountID": "***************",
							"productVariantID": "microsaver",
							"status": "ACTIVE"
						  }
						],
						"data": [
						  {
							"accountID": "***************",
							"accountName": "",
							"accountType": "Celengan"
						  }
						]
					  },
					  "meta": null,
					  "safeID": "c0575d1f-97a6-494d-8228-42711ae1fdea"
					}
				}`

				response, err := client.Post(CustomerSearch, body, xfccHeader)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
				Expect(response.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("user has valid permissions and getting piggybank account", func() {
			It("should successfully return customer search results", func() {
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "c0575d1f-97a6-494d-8228-42711ae1fdea",
    				"identifierType": "SAFE_ID",
   					"key": "piggybank"
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
						AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("piggybank", 1). // keyword and status
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id",
						"order", "level", "status", "type",
					}).AddRow(
						1, "Piggybank", "piggybank", nil, 1, 3, 1, "sectionArray",
					))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRole)).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order"}).
						AddRow(1, "Account ID", "piggyAccountID", "oneliner", 1, 1).
						AddRow(2, "Interest Rate", "piggyInterestRate", "oneliner", 1, 2).
						AddRow(3, "Balance With Interest Payout", "balanceWithInterest", "oneliner", 1, 3))

				productVariantIDs := constants.ProductVariantMappingBySubTab[constants.KeyPiggybank]

				var mockResponseCust *customerMaster.GetCustomerByIdentifierResponse
				_ = json.Unmarshal([]byte(resources.ExpectedCustomerLeanResponse), &mockResponseCust)

				// mock for GetCustomerLean
				mockCustomerMaster.On("GetCustomerLean", mock.Anything, &customerMaster.GetCustomerLeanRequest{
					Identifier:     "c0575d1f-97a6-494d-8228-42711ae1fdea",
					IdentifierType: customerMaster.IdentifierType_SAFE_ID,
					ServiceID:      constants.DIGIBANK,
					Data:           []customerMaster.DataFetchedType{customerMaster.Data_PersonalInfo},
				}).Return(mockResponseCust, nil).Once()

				var mockResponse *accountService.ListCASAAccountsForCustomerDetailResponse
				_ = json.Unmarshal([]byte(resources.ExpectedResponseGetAccountListPiggybank), &mockResponse)

				// mock for get
				mockAccountService.On("ListCASAAccountsForCustomerDetail", mock.Anything, &accountService.ListCASAAccountsForCustomerDetailRequest{
					CifNumber:         "ID179555577230",
					FetchBalance:      true,
					FetchInterestRate: true,
					ProductVariantIDs: productVariantIDs,
				}).Return(mockResponse, nil).Once()

				var expectedResponseLastTrxList = `{
					"links": {
						"next": "/v1/accounts/************/transactions?pageSize=1&startingBefore=************************************************************************************************************************************",
						"nextCursorID": "************************************************************************************************************************************",
						"prev": "",
						"prevCursorID": ""
					},
					"data": [
						{
							"transactionID": "pt02_dm01_************_5_se01_************_1743094860000000000",
							"batchID": "************_5_se01_************_1743094860000000000",
							"displayName": "Bunga Didapat",
							"iconURL": "https://idbank-dev-backend-assets.s3.ap-southeast-3.amazonaws.com/interest_earned.png",
							"amount": 32,
							"currency": "IDR",
							"status": "COMPLETED",
							"creationTimestamp": "2025-03-27T17:01:00Z",
							"category": {},
							"endingBalance": 8434,
							"transactionType": "INTEREST_PAYOUT",
							"transactionSubtype": "SAVINGS",
							"transactionDescription": "Bunga Didapat",
							"counterParty": {
								"displayName": "Bunga Didapat",
								"iconURL": "https://idbank-dev-backend-assets.s3.ap-southeast-3.amazonaws.com/interest_earned.png"
							},
							"updateTimestamp": "2025-03-27T17:01:00Z"
						}
					]
				}`

				redisKey := fmt.Sprintf("%s_%s_%s", constants.RedisFirstKeyCustomerDataPoint, constants.KeyPiggybankGoalAmount, constants.ProductVariantPiggybank)
				// get from redis
				mockRedis.On("GetString", mock.Anything, redisKey).Return("5,000,000.00", nil)

				var mockTxResponse *txHistory.GetTransactionsHistoryResponse
				_ = json.Unmarshal([]byte(expectedResponseLastTrxList), &mockTxResponse)

				mockTxHistory.On("ListAccountTransactionsSearch", mock.Anything, &txHistory.GetTransactionsHistoryRequest{
					AccountID: "***************",
					PageSize:  1,
				}).Return(mockTxResponse, nil).Once()

				expectedResponse := `{
					"status": "Success",
					"structure": {
					  "key": "piggybank",
					  "type": "sectionArray",
					  "label": "Piggybank",
					  "children": [
						{
						  "key": "piggyAccountID",
						  "type": "oneliner",
						  "label": "Account ID"
						},
						{
						  "key": "piggyInterestRate",
						  "type": "oneliner",
						  "label": "Interest Rate"
						},
						{
						  "key": "balanceWithInterest",
						  "type": "oneliner",
						  "label": "Balance With Interest Payout"
						}
					  ]
					},
					"data": {
					  "cif": "ID179555577230",
					  "meta": null,
					  "piggybank": {
						"accounts": [
						  {
							"accountID": "***************",
							"productVariantID": "microsaver",
							"status": "ACTIVE"
						  }
						],
						"data": [
						  {
							"balanceWithInterest": "0.00",
							"piggyAccountID": "***************",
							"piggyInterestRate": "0.00 % p.a"
						  }
						]
					  },
					  "safeID": "c0575d1f-97a6-494d-8228-42711ae1fdea"
					}
				}`

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
				Expect(response.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("user has valid permissions and getting term deposit account", func() {
			It("should successfully return customer search results", func() {
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "c0575d1f-97a6-494d-8228-42711ae1fdea",
    				"identifierType": "SAFE_ID",
   					"key": "termDeposit"
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
						AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("termDeposit", 1). // keyword and status
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id",
						"order", "level", "status", "type",
					}).AddRow(
						1, "Term Deposit", "termDeposit", nil, 1, 3, 1, "sectionArray",
					))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRole)).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order"}).
						AddRow(1, "Account ID", "tdAccountID", "oneliner", 1, 1).
						AddRow(2, "Principal Amount", "principalAmount", "oneliner", 1, 2).
						AddRow(3, "Total Balance with Estimated Earned Interest", "balanceWithEstimateInterest", "oneliner", 1, 3))

				productVariantIDs := constants.ProductVariantMappingBySubTab[constants.KeyTermDeposit]

				var mockResponseCust *customerMaster.GetCustomerByIdentifierResponse
				_ = json.Unmarshal([]byte(resources.ExpectedCustomerLeanResponse), &mockResponseCust)

				// mock for GetCustomerLean
				mockCustomerMaster.On("GetCustomerLean", mock.Anything, &customerMaster.GetCustomerLeanRequest{
					Identifier:     "c0575d1f-97a6-494d-8228-42711ae1fdea",
					IdentifierType: customerMaster.IdentifierType_SAFE_ID,
					ServiceID:      constants.DIGIBANK,
					Data:           []customerMaster.DataFetchedType{customerMaster.Data_PersonalInfo},
				}).Return(mockResponseCust, nil).Once()

				var mockResponse *accountService.ListCASAAccountsForCustomerDetailResponse
				_ = json.Unmarshal([]byte(resources.ExpectedResponseGetAccountListTermDeposit), &mockResponse)

				// mock for get
				mockAccountService.On("ListCASAAccountsForCustomerDetail", mock.Anything, &accountService.ListCASAAccountsForCustomerDetailRequest{
					CifNumber:         "ID179555577230",
					FetchBalance:      true,
					FetchInterestRate: true,
					ProductVariantIDs: productVariantIDs,
				}).Return(mockResponse, nil).Once()

				expectedResponse := `{
					"status": "Success",
					"structure": {
						"key": "termDeposit",
						"type": "sectionArray",
						"label": "Term Deposit",
						"children": [
							{
								"key": "tdAccountID",
								"type": "oneliner",
								"label": "Account ID"
							},
							{
								"key": "principalAmount",
								"type": "oneliner",
								"label": "Principal Amount"
							},
							{
								"key": "balanceWithEstimateInterest",
								"type": "oneliner",
								"label": "Total Balance with Estimated Earned Interest"
							}
						]
					},
					"data": {
						"cif": "ID179555577230",
						"meta": null,
						"safeID": "c0575d1f-97a6-494d-8228-42711ae1fdea",
						"termDeposit": {
							"accounts": [
								{
									"accountID": "************",
									"productVariantID": "term_deposit_default",
									"status": "ACTIVE"
								}
							],
							"data": [
								{
									"balanceWithEstimateInterest": "500,171.23",
									"principalAmount": "500,000.00",
									"tdAccountID": "************"
								}
							]
						}
					}
				}`

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
				Expect(response.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("user has valid permissions and getting term deposit param history", func() {
			It("should successfully return customer search results", func() {
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "c0575d1f-97a6-494d-8228-42711ae1fdea",
    				"identifierType": "SAFE_ID",
   					"key": "depositParameterHistory",
					"payload": {
						"accountID": "************"
					}
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
						AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("depositParameterHistory", 1). // keyword and status
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id",
						"order", "level", "status", "type",
					}).AddRow(
						1, "Change Maturity Instruction", "depositParameterHistory", nil, 1, 4, 1, "table",
					))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRole)).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order"}).
						AddRow(1, "Timestamp", "instructionTimestamp", "oneliner", 1, 1).
						AddRow(2, "Maturity Instruction Before", "maturityInstructionBefore", "oneliner", 1, 2).
						AddRow(3, "Maturity Instruction After", "maturityInstructionAfter", "oneliner", 1, 3))

				var mockResponse *accountService.GetDepositsParameterHistoryResponse
				_ = json.Unmarshal([]byte(resources.ExpectedResponseTermDepositParamHistory), &mockResponse)

				// mock for get
				mockAccountService.On("GetDepositsParameterHistory", mock.Anything, &accountService.GetDepositsParameterHistoryRequest{
					AccountID:    "************",
					ParameterKey: constants.TermDepositParameterKeyMaturityInstruction,
				}).Return(mockResponse, nil).Once()

				expectedResponse := `{
					"status": "Success",
					"structure": {
						"key": "depositParameterHistory",
						"type": "table",
						"label": "Change Maturity Instruction",
						"children": [
							{
								"key": "instructionTimestamp",
								"type": "oneliner",
								"label": "Timestamp"
							},
							{
								"key": "maturityInstructionBefore",
								"type": "oneliner",
								"label": "Maturity Instruction Before"
							},
							{
								"key": "maturityInstructionAfter",
								"type": "oneliner",
								"label": "Maturity Instruction After"
							}
						]
					},
					"data": {
						"cif": null,
						"depositParameterHistory": [
							{
								"instructionTimestamp": "10 Jul 2024 16:05:24",
								"maturityInstructionAfter": "PRINCIPAL_ONLY_ARO",
								"maturityInstructionBefore": ""
							}
						],
						"meta": null,
						"safeID": "c0575d1f-97a6-494d-8228-42711ae1fdea"
					}
				}`

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
				Expect(response.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("user has valid permissions and getting term deposit renewal history", func() {
			It("should successfully return customer search results", func() {
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "c0575d1f-97a6-494d-8228-42711ae1fdea",
    				"identifierType": "SAFE_ID",
   					"key": "renewalHistory",
					"payload": {
						"accountID": "************"
					}
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
						AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("renewalHistory", 1). // keyword and status
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id",
						"order", "level", "status", "type",
					}).AddRow(
						1, "Renewal History", "renewalHistory", nil, 1, 4, 1, "table",
					))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRole)).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order"}).
						AddRow(1, "Timestamp", "renewTimestamp", "oneliner", 1, 1).
						AddRow(2, "Amount", "renewAmount", "oneliner", 1, 2).
						AddRow(3, "Maturity Date", "renewMaturityDate", "oneliner", 1, 3))

				var mockResponse *accountService.DepositsParameterRenewalHistoryResponse
				_ = json.Unmarshal([]byte(resources.ExpectedResponseTermDepositRenewalHistory), &mockResponse)

				// mock for get
				mockAccountService.On("GetDepositsParameterRenewalHistory", mock.Anything, &accountService.DepositsParameterRenewalHistoryRequest{
					AccountID: "************",
				}).Return(mockResponse, nil).Once()

				expectedResponse := `{
					"status": "Success",
					"structure": {
						"key": "renewalHistory",
						"type": "table",
						"label": "Renewal History",
						"children": [
							{
								"key": "renewTimestamp",
								"type": "oneliner",
								"label": "Timestamp"
							},
							{
								"key": "renewAmount",
								"type": "oneliner",
								"label": "Amount"
							},
							{
								"key": "renewMaturityDate",
								"type": "oneliner",
								"label": "Maturity Date"
							}
						]
					},
					"data": {
						"cif": null,
						"meta": null,
						"renewalHistory": [
							{
								"renewAmount": "0.00",
								"renewMaturityDate": "2024-07-12",
								"renewTimestamp": "10 Jul 2024 16:05:24"
							}
						],
						"safeID": "c0575d1f-97a6-494d-8228-42711ae1fdea"
					}
				}`

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
				Expect(response.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("user has valid permission and getting transaction config tab", func() {
			It("should successfully return customer search results", func() {
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "c0575d1f-97a6-494d-8228-42711ae1fdea",
    				"identifierType": "SAFE_ID",
   					"key": "transactionConfiguration"
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
						AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("transactionConfiguration", 1). // keyword and status
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id",
						"order", "level", "status", "type",
					}).AddRow(
						3, "Transaction Configuration", "transactionConfiguration", 2, 1, 1, 1, "dropdown",
					))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRole)).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order"}).
						AddRow(4, "Customer Level - Transaction Limit", "customerLevelTxLimit", "table", 3, 1).
						AddRow(5, "Bank Wide Level - Transaction Limit", "bankWideLevelTxLimit", "table", 3, 2))

				expectedResponse := `{
					"status": "Success",
					"structure": {
						"key": "transactionConfiguration",
						"type": "dropdown",
						"label": "Transaction Configuration",
						"children": [
							{
								"key": "customerLevelTxLimit",
								"type": "table",
								"label": "Customer Level - Transaction Limit"
							},
							{
								"key": "bankWideLevelTxLimit",
								"type": "table",
								"label": "Bank Wide Level - Transaction Limit"
							}
						]
					},
					"data": {
						"cif": null,
						"meta": null,
						"safeID": "c0575d1f-97a6-494d-8228-42711ae1fdea"
					}
				}`

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
				Expect(response.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("user has valid permission and getting bank wide tx config", func() {
			It("should successfully return customer search results", func() {
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "6e78f7a2-1299-404f-b874-dc66d482a1ff",
    				"identifierType": "SAFE_ID",
   					"key": "bankWideLevelTxLimit"
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
						AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("bankWideLevelTxLimit", 1). // keyword and status
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id",
						"order", "level", "status", "type",
					}).AddRow(
						3, "Bank Wide Level - Transaction Limit", "bankWideLevelTxLimit", 2, 1, 1, 1, "table",
					))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRole)).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order"}).
						AddRow(4, "Timestamp", "bankLimitTimestamp", "oneliner_datetime", 3, 1).
						AddRow(5, "Type", "bankLimitType", "oneliner", 3, 2).
						AddRow(6, "Frequency", "bankLimitFrequency", "oneliner", 3, 3))

				expectedTxLimitResponse := `{
					"data": [
						{
							"limitName": "RET_INTER_BIFAST",
							"limitRuleName": "RET_INTER_BIFAST.CUSTOMER_ID.ONCE",
							"conditionKey": "CUSTOMER_ID",
							"currency": "IDR",
							"minimumAmount": 100000,
							"maximumAmount": 1********00,
							"minimumAllowed": 100000,
							"maximumAllowed": 2********00,
							"frequency": "ONCE",
							"cumulativeAmount": 0,
							"createdAt": "2024-09-05T07:46:33Z",
							"updatedAt": "2024-10-01T04:40:01Z",
							"status": "ACTIVE"
						},
						{
							"limitName": "RET_INTER_BIFAST",
							"limitRuleName": "RET_INTER_BIFAST.CUSTOMER_ID.DAILY_CALENDAR",
							"conditionKey": "CUSTOMER_ID",
							"currency": "IDR",
							"minimumAmount": 1000000,
							"maximumAmount": ********,
							"minimumAllowed": 100000,
							"maximumAllowed": *********,
							"frequency": "DAILY_CALENDAR",
							"cumulativeAmount": 0,
							"createdAt": "2024-09-23T10:18:24Z",
							"updatedAt": "2024-09-23T10:18:24Z",
							"status": "ACTIVE"
						}
					]
				}`

				var mockResponse *transactionLimitAPI.GetTransactionLimitRulesResponse
				_ = json.Unmarshal([]byte(expectedTxLimitResponse), &mockResponse)

				mockTxLimit.On("ListTransactionLimitRules", mock.Anything, &transactionLimitAPI.GetTransactionLimitRulesRequest{
					LimitNames:        []string{constants.FetchTransferLimitNameBIFast},
					LimitRuleStatuses: []string{constants.LimitStatusActive},
				}).Return(mockResponse, nil).Once()

				expectedResponse := `{
					"status": "Success",
					"structure": {
						"key": "bankWideLevelTxLimit",
						"type": "table",
						"label": "Bank Wide Level - Transaction Limit",
						"children": [
							{
								"key": "bankLimitTimestamp",
								"type": "oneliner_datetime",
								"label": "Timestamp"
							},
							{
								"key": "bankLimitType",
								"type": "oneliner",
								"label": "Type"
							},
							{
								"key": "bankLimitFrequency",
								"type": "oneliner",
								"label": "Frequency"
							}
						]
					},
					"data": {
						"bankWideLevelTxLimit": [
							{
								"bankLimitFrequency": "per Transaction",
								"bankLimitTimestamp": "2024-09-05T07:46:33Z",
								"bankLimitType": "BI-FAST"
							},
							{
								"bankLimitFrequency": "Daily",
								"bankLimitTimestamp": "2024-09-23T10:18:24Z",
								"bankLimitType": "BI-FAST"
							}
						],
						"cif": null,
						"meta": null,
						"safeID": "6e78f7a2-1299-404f-b874-dc66d482a1ff"
					}
				}`

				response, err := client.Post(CustomerSearch, body, xfccHeader)
				fmt.Println(response)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
				Expect(response.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("user has valid permission and getting customer level tx config", func() {
			It("should successfully return customer search results", func() {
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "6e78f7a2-1299-404f-b874-dc66d482a1ff",
					"identifierType": "SAFE_ID",
					"key": "customerLevelTxLimit"
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
						AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("customerLevelTxLimit", 1). // keyword and status
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id",
						"order", "level", "status", "type",
					}).AddRow(
						3, "Customer Level - Transaction Limit", "customerLevelTxLimit", 2, 1, 1, 1, "table",
					))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRole)).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order"}).
						AddRow(4, "Timestamp", "custLimitTimestamp", "oneliner_datetime", 3, 1).
						AddRow(5, "Type", "custLimitType", "oneliner", 3, 2).
						AddRow(6, "Frequency", "custLimitFrequency", "oneliner", 3, 3))

				expectedTxLimitResponse := `{
					"data": [
						{
							"limitName": "RET_INTER_BIFAST",
							"limitRuleName": "RET_INTER_BIFAST.CUSTOMER_ID.ONCE",
							"conditionKey": "CUSTOMER_ID",
							"currency": "IDR",
							"minimumAmount": 100000,
							"maximumAmount": 1********00,
							"minimumAllowed": 100000,
							"maximumAllowed": 2********00,
							"frequency": "ONCE",
							"cumulativeAmount": 0,
							"createdAt": "2024-09-05T07:46:33Z",
							"updatedAt": "2024-10-01T04:40:01Z"
						},
						{
							"limitName": "RET_INTER_BIFAST",
							"limitRuleName": "RET_INTER_BIFAST.CUSTOMER_ID.DAILY_CALENDAR",
							"conditionKey": "CUSTOMER_ID",
							"currency": "IDR",
							"minimumAmount": 1000000,
							"maximumAmount": ********,
							"minimumAllowed": 100000,
							"maximumAllowed": *********,
							"frequency": "DAILY_CALENDAR",
							"cumulativeAmount": 0,
							"createdAt": "2024-09-23T10:18:24Z",
							"updatedAt": "2024-09-23T10:18:24Z"
						}
					]
				}`

				var mockResponse *transactionLimitAPI.GetTransactionLimitResponse
				_ = json.Unmarshal([]byte(expectedTxLimitResponse), &mockResponse)

				mockTxLimit.On("SearchTransactionLimitRulesV2", mock.Anything, &transactionLimitAPI.GetTransactionLimitRequestV2{
					LimitNames: []interface{}{constants.FetchTransferLimitNameBIFast},
				}).Return(mockResponse, nil).Once()

				expectedResponse := `{
					"status": "Success",
					"structure": {
						"key": "customerLevelTxLimit",
						"type": "table",
						"label": "Customer Level - Transaction Limit",
						"children": [
							{
								"key": "custLimitTimestamp",
								"type": "oneliner_datetime",
								"label": "Timestamp"
							},
							{
								"key": "custLimitType",
								"type": "oneliner",
								"label": "Type"
							},
							{
								"key": "custLimitFrequency",
								"type": "oneliner",
								"label": "Frequency"
							}
						]
					},
					"data": {
						"cif": null,
						"customerLevelTxLimit": [
							{
								"custLimitFrequency": "per Transaction",
								"custLimitTimestamp": "2024-09-05T07:46:33Z",
								"custLimitType": "BI-FAST"
							},
							{
								"custLimitFrequency": "Daily",
								"custLimitTimestamp": "2024-09-23T10:18:24Z",
								"custLimitType": "BI-FAST"
							}
						],
						"meta": null,
						"safeID": "6e78f7a2-1299-404f-b874-dc66d482a1ff"
					}
				}`

				response, err := client.Post(CustomerSearch, body, xfccHeader)
				fmt.Println(response)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
				Expect(response.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("user has valid permission and getting external linkage", func() {
			It("should successfully return customer search results", func() {
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "6e78f7a2-1299-404f-b874-dc66d482a1ff",
					"identifierType": "SAFE_ID",
					"key": "externalLinkages"
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
						AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("externalLinkages", 1). // keyword and status
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id",
						"order", "level", "status", "type",
					}).AddRow(
						3, "External Linkages", "externalLinkages", 2, 1, 1, 1, "dropdown",
					))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRole)).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order"}).
						AddRow(4, "Partner Linkage", "partnerLinkage", "sectionArray", 3, 1))

				expectedPayAuthzResponse := `{
					"listPartner": [
						{
							"billingAgreementID": "779c1bc17d674230b2f049fdf09befba",
							"partnerID": "0c1fc047-1163-48ce-a493-808ba4feca89",
							"status": "TERMINATED",
							"createdAt": "2023-11-16T07:15:07Z",
							"icon": "https://idbank-dev-backend-payment-public-assets.s3.ap-southeast-3.amazonaws.com/ecosystem/grab-circle.webp",
							"dailyTransactionLimit": ********,
							"usedDailyTransactionLimit": 0,
							"partnerName": "Grab",
							"limitName": "GRAB_ECOSYSTEM_LIMIT",
							"updatedAt": "2024-07-05T01:43:17Z",
							"transactionLimits": [
								{
									"limitName": "GRAB_ECOSYSTEM_LIMIT",
									"limitRuleName": "GRAB_ECOSYSTEM_LIMIT.CUSTOMER_ID.DAILY_CALENDAR",
									"conditionKey": "CUSTOMER_ID",
									"conditionValue": "6e78f7a2-1299-404f-b874-dc66d482a1ff",
									"currency": "IDR",
									"minimumAmount": 100,
									"maximumAmount": ********,
									"minimumAllowed": 100,
									"maximumAllowed": *********,
									"frequency": "DAILY_CALENDAR",
									"cumulativeAmount": 0
								}
							]
						},
						{
							"billingAgreementID": "4104e0e950c04de784cb22453f2830c8",
							"partnerID": "117845cc-b841-4a59-a294-a286b65e698a",
							"status": "TERMINATED",
							"createdAt": "2024-07-01T09:46:43Z",
							"icon": "https://idbank-dev-backend-payment-public-assets.s3.ap-southeast-3.amazonaws.com/ecosystem/ovo-circle.webp",
							"dailyTransactionLimit": **********,
							"usedDailyTransactionLimit": 0,
							"partnerName": "OVO",
							"limitName": "OVO_ECOSYSTEM_LIMIT",
							"updatedAt": "2024-11-13T05:18:42Z",
							"transactionLimits": [
								{
									"limitName": "OVO_ECOSYSTEM_LIMIT",
									"limitRuleName": "OVO_ECOSYSTEM_LIMIT.CUSTOMER_ID.DAILY_CALENDAR",
									"conditionKey": "CUSTOMER_ID",
									"currency": "IDR",
									"minimumAmount": 100,
									"maximumAmount": **********,
									"minimumAllowed": 100,
									"maximumAllowed": **********,
									"frequency": "DAILY_CALENDAR",
									"cumulativeAmount": 0
								},
								{
									"limitName": "OVO_ECOSYSTEM_LIMIT",
									"limitRuleName": "OVO_ECOSYSTEM_LIMIT.CUSTOMER_ID.ONCE",
									"conditionKey": "CUSTOMER_ID",
									"currency": "IDR",
									"minimumAmount": 100,
									"maximumAmount": 561310000,
									"minimumAllowed": 100,
									"maximumAllowed": 2000000000,
									"frequency": "ONCE",
									"cumulativeAmount": 0
								}
							]
						}
					]
				}`

				var mockResponse *PayAuthZApi.ListPartnerResponse
				_ = json.Unmarshal([]byte(expectedPayAuthzResponse), &mockResponse)

				mockPayAuthz.On("ListPartnerEndpoint", mock.Anything, &PayAuthZApi.ListPartnerRequest{
					SafeID: "6e78f7a2-1299-404f-b874-dc66d482a1ff"}).Return(mockResponse, nil).Once()

				expectedResponse := `{
					"status": "Success",
					"structure": {
						"key": "externalLinkages",
						"type": "dropdown",
						"label": "External Linkages",
						"children": [
							{
								"key": "partnerLinkage",
								"type": "sectionArray",
								"label": "Partner Linkage"
							}
						]
					},
					"data": {
						"cif": null,
						"meta": null,
						"safeID": "6e78f7a2-1299-404f-b874-dc66d482a1ff"
					}
				}`

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
				Expect(response.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("user has valid permission and getting partner linkage detail", func() {
			It("should successfully return customer search results", func() {
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "6e78f7a2-1299-404f-b874-dc66d482a1ff",
					"identifierType": "SAFE_ID",
					"key": "partnerLinkage"
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
						AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("partnerLinkage", 1). // keyword and status
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id",
						"order", "level", "status", "type",
					}).AddRow(
						3, "Partner Linkage", "partnerLinkage", 3, 1, 1, 1, "sectionArray",
					))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRole)).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order"}).
						AddRow(4, "Ecosystem ID | Partner User ID", "partnerUserID", "oneliner", 3, 1).
						AddRow(5, "Daily Transaction Limit Set", "partnerDailyTxLimitSet", "oneliner", 3, 2).
						AddRow(6, "Linked Account", "partnerLinkedAccount", "oneliner", 3, 3).
						AddRow(7, "Status Notes", "partnerStatusNotes", "oneliner", 3, 4).
						AddRow(8, "Onboarding Channel", "partnerOnboardingChannel", "oneliner", 3, 5))

				expectedPayAuthzResponse := `{
					"listPartner": [
						{
							"billingAgreementID": "779c1bc17d674230b2f049fdf09befba",
							"partnerID": "0c1fc047-1163-48ce-a493-808ba4feca89",
							"status": "TERMINATED",
							"createdAt": "2023-11-16T07:15:07Z",
							"icon": "https://idbank-dev-backend-payment-public-assets.s3.ap-southeast-3.amazonaws.com/ecosystem/grab-circle.webp",
							"dailyTransactionLimit": ********,
							"usedDailyTransactionLimit": 0,
							"partnerName": "Grab",
							"limitName": "GRAB_ECOSYSTEM_LIMIT",
							"updatedAt": "2024-07-05T01:43:17Z",
							"transactionLimits": [
								{
									"limitName": "GRAB_ECOSYSTEM_LIMIT",
									"limitRuleName": "GRAB_ECOSYSTEM_LIMIT.CUSTOMER_ID.DAILY_CALENDAR",
									"conditionKey": "CUSTOMER_ID",
									"conditionValue": "6e78f7a2-1299-404f-b874-dc66d482a1ff",
									"currency": "IDR",
									"minimumAmount": 100,
									"maximumAmount": ********,
									"minimumAllowed": 100,
									"maximumAllowed": *********,
									"frequency": "DAILY_CALENDAR",
									"cumulativeAmount": 0
								}
							]
						},
						{
							"billingAgreementID": "4104e0e950c04de784cb22453f2830c8",
							"partnerID": "117845cc-b841-4a59-a294-a286b65e698a",
							"status": "TERMINATED",
							"createdAt": "2024-07-01T09:46:43Z",
							"icon": "https://idbank-dev-backend-payment-public-assets.s3.ap-southeast-3.amazonaws.com/ecosystem/ovo-circle.webp",
							"dailyTransactionLimit": **********,
							"usedDailyTransactionLimit": 0,
							"partnerName": "OVO",
							"limitName": "OVO_ECOSYSTEM_LIMIT",
							"updatedAt": "2024-11-13T05:18:42Z",
							"transactionLimits": [
								{
									"limitName": "OVO_ECOSYSTEM_LIMIT",
									"limitRuleName": "OVO_ECOSYSTEM_LIMIT.CUSTOMER_ID.DAILY_CALENDAR",
									"conditionKey": "CUSTOMER_ID",
									"currency": "IDR",
									"minimumAmount": 100,
									"maximumAmount": **********,
									"minimumAllowed": 100,
									"maximumAllowed": **********,
									"frequency": "DAILY_CALENDAR",
									"cumulativeAmount": 0
								},
								{
									"limitName": "OVO_ECOSYSTEM_LIMIT",
									"limitRuleName": "OVO_ECOSYSTEM_LIMIT.CUSTOMER_ID.ONCE",
									"conditionKey": "CUSTOMER_ID",
									"currency": "IDR",
									"minimumAmount": 100,
									"maximumAmount": 561310000,
									"minimumAllowed": 100,
									"maximumAllowed": 2000000000,
									"frequency": "ONCE",
									"cumulativeAmount": 0
								}
							]
						}
					]
				}`

				var mockResponse *PayAuthZApi.ListPartnerResponse
				_ = json.Unmarshal([]byte(expectedPayAuthzResponse), &mockResponse)

				mockPayAuthz.On("ListPartnerEndpoint", mock.Anything, &PayAuthZApi.ListPartnerRequest{
					SafeID: "6e78f7a2-1299-404f-b874-dc66d482a1ff"}).Return(mockResponse, nil).Once()

				linkedData := map[string]interface{}{
					"Grab": "************",
					"OVO":  "************",
				}

				// get from redis
				linkedRedisKey := fmt.Sprintf("%s_%s_%s", constants.RedisFirstKeyCustomerDataPoint, constants.KeyEcosystemPartnerLinkedAccount, "6e78f7a2-1299-404f-b874-dc66d482a1ff")

				binaryLinkedData, _ := json.Marshal(linkedData)
				mockRedis.On("GetString", mock.Anything, linkedRedisKey).Return(string(binaryLinkedData), nil)

				// get from redis
				channelRedisKey := fmt.Sprintf("%s_%s_%s", constants.RedisFirstKeyCustomerDataPoint, constants.KeyEcosystemOnboardingChannel, "6e78f7a2-1299-404f-b874-dc66d482a1ff")
				channelBinaryData, _ := json.Marshal("MANUAL")
				mockRedis.On("GetString", mock.Anything, channelRedisKey).Return(string(channelBinaryData), nil)

				expectedResponse := `{
					"status": "Success",
					"structure": {
						"key": "partnerLinkage",
						"type": "sectionArray",
						"label": "Partner Linkage",
						"children": [
							{
								"key": "partnerUserID",
								"type": "oneliner",
								"label": "Ecosystem ID | Partner User ID"
							},
							{
								"key": "partnerDailyTxLimitSet",
								"type": "oneliner",
								"label": "Daily Transaction Limit Set"
							},
							{
								"key": "partnerLinkedAccount",
								"type": "oneliner",
								"label": "Linked Account"
							},
							{
								"key": "partnerStatusNotes",
								"type": "oneliner",
								"label": "Status Notes"
							},
							{
								"key": "partnerOnboardingChannel",
								"type": "oneliner",
								"label": "Onboarding Channel"
							}
						]
					},
					"data": {
						"cif": null,
						"meta": null,
						"partnerLinkage": {
							"data": [
								{
									"partnerDailyTxLimitSet": "500,000.00",
									"partnerLinkedAccount": "************",
									"partnerOnboardingChannel": "MANUAL",
									"partnerStatusNotes": "Unlinked",
									"partnerUserID": "779c1bc17d674230b2f049fdf09befba"
								},
								{
									"partnerDailyTxLimitSet": "30,000,000.00",
									"partnerLinkedAccount": "************",
									"partnerOnboardingChannel": "MANUAL",
									"partnerStatusNotes": "Unlinked",
									"partnerUserID": "4104e0e950c04de784cb22453f2830c8"
								}
							],
							"partners": [
								{
									"partnerID": "0c1fc047-1163-48ce-a493-808ba4feca89",
									"partnerName": "Grab",
									"partnerUserID": "779c1bc17d674230b2f049fdf09befba"
								},
								{
									"partnerID": "117845cc-b841-4a59-a294-a286b65e698a",
									"partnerName": "OVO",
									"partnerUserID": "4104e0e950c04de784cb22453f2830c8"
								}
							]
						},
						"safeID": "6e78f7a2-1299-404f-b874-dc66d482a1ff"
					}
				}`

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
				Expect(response.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("user has valid permission and getting linking history", func() {
			It("should successfully return customer search results", func() {
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "6e78f7a2-1299-404f-b874-dc66d482a1ff",
					"identifierType": "SAFE_ID",
					"key": "linkingHistory",
					"payload": {
						"partnerID": "0c1fc047-1163-48ce-a493-808ba4feca89"
					}
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
						AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("linkingHistory", 1). // keyword and status
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id",
						"order", "level", "status", "type",
					}).AddRow(
						3, "Linking History", "linkingHistory", 3, 1, 1, 1, "table",
					))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRole)).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order"}).
						AddRow(4, "Timestamp", "linkingTimestamp", "oneliner_datetime", 3, 1).
						AddRow(5, "Previous Limit", "linkingPreviousLimit", "oneliner", 3, 2).
						AddRow(6, "Action By", "linkingActionBy", "oneliner", 3, 3))

				expectedCustJournalResponse := `{
					"links": {
						"prevCursorID": "eyJjdXN0b21lcklEIjoiNmU3OGY3YTItMTI5OS00MDRmLWI4NzQtZGM2NmQ0ODJhMWZmXzBjMWZjMDQ3LTExNjMtNDhjZS1hNDkzLTgwOGJhNGZlY2E4OSIsImV2ZW50VGltZXN0YW1wIjoiMTcyMDE0Mzc5NjI3ODI0NDczNCJ9",
						"nextCursorID": "eyJjdXN0b21lcklEIjoiNmU3OGY3YTItMTI5OS00MDRmLWI4NzQtZGM2NmQ0ODJhMWZmXzBjMWZjMDQ3LTExNjMtNDhjZS1hNDkzLTgwOGJhNGZlY2E4OSIsImV2ZW50VGltZXN0YW1wIjoiMTcxNTE1NDMxMTkzMDE0ODI5NiJ9"
					},
					"data": [
						{
							"customerID": "6e78f7a2-1299-404f-b874-dc66d482a1ff_0c1fc047-1163-48ce-a493-808ba4feca89",
							"eventTimestamp": "1720143796278244734",
							"metadata": {
								"action": "Unlink",
								"actionBy": "customer-portal - Ops - Appian Administrator",
								"actionSystem": "Superbank CE",
								"billingAgreementID": "779c1bc17d674230b2f049fdf09befba",
								"currentLimit": "0",
								"failureReason": null,
								"partnerID": "0c1fc047-1163-48ce-a493-808ba4feca89",
								"partnerName": "Grab",
								"previousLimit": "0",
								"status": "SUCCESS",
								"userSafeID": "6e78f7a2-1299-404f-b874-dc66d482a1ff"
							}
						},
						{
							"customerID": "6e78f7a2-1299-404f-b874-dc66d482a1ff_0c1fc047-1163-48ce-a493-808ba4feca89",
							"eventTimestamp": "1715154313117959093",
							"metadata": {
								"action": "Unlink",
								"actionBy": "customer-portal - Ops - Core Banking Maker 1",
								"actionSystem": "Superbank CE",
								"billingAgreementID": "779c1bc17d674230b2f049fdf09befba",
								"currentLimit": "0",
								"failureReason": "Can't terminate transaction because there's transaction pending",
								"partnerID": "0c1fc047-1163-48ce-a493-808ba4feca89",
								"partnerName": "Grab",
								"previousLimit": "0",
								"status": "FAILED",
								"userSafeID": "6e78f7a2-1299-404f-b874-dc66d482a1ff"
							}
						},
						{
							"customerID": "6e78f7a2-1299-404f-b874-dc66d482a1ff_0c1fc047-1163-48ce-a493-808ba4feca89",
							"eventTimestamp": "1715154311930148296",
							"metadata": {
								"action": "Unlink",
								"actionBy": "customer-portal - Ops - Core Banking Maker 1",
								"actionSystem": "Superbank CE",
								"billingAgreementID": "779c1bc17d674230b2f049fdf09befba",
								"currentLimit": "0",
								"failureReason": "Can't terminate transaction because there's transaction pending",
								"partnerID": "0c1fc047-1163-48ce-a493-808ba4feca89",
								"partnerName": "Grab",
								"previousLimit": "0",
								"status": "FAILED",
								"userSafeID": "6e78f7a2-1299-404f-b874-dc66d482a1ff"
							}
						}
					]
				}`

				var mockResponse *customerJournalAPI.Response
				_ = json.Unmarshal([]byte(expectedCustJournalResponse), &mockResponse)

				mockCustomerJournal.On("GetCustomerJournalData", mock.Anything, &customerJournalAPI.Request{
					UserSafeID: fmt.Sprintf("%s_%s", "6e78f7a2-1299-404f-b874-dc66d482a1ff", "0c1fc047-1163-48ce-a493-808ba4feca89"),
					PageSize:   10,
					Endpoint:   constants.CustomerJournalLogTypeEcosystem,
				}).Return(mockResponse, nil).Once()

				expectedResponse := `{
					"status": "Success",
					"structure": {
						"key": "linkingHistory",
						"type": "table",
						"label": "Linking History",
						"children": [
							{
								"key": "linkingTimestamp",
								"type": "oneliner_datetime",
								"label": "Timestamp"
							},
							{
								"key": "linkingPreviousLimit",
								"type": "oneliner",
								"label": "Previous Limit"
							},
							{
								"key": "linkingActionBy",
								"type": "oneliner",
								"label": "Action By"
							}
						]
					},
					"data": {
						"cif": null,
						"linkingHistory": {
							"data": [
								{
									"linkingActionBy": "Ops -  Appian Administrator",
									"linkingPreviousLimit": "",
									"linkingTimestamp": "2024-07-05T08:43:16Z"
								},
								{
									"linkingActionBy": "Ops -  Core Banking Maker 1",
									"linkingPreviousLimit": "",
									"linkingTimestamp": "2024-05-08T14:45:13Z"
								},
								{
									"linkingActionBy": "Ops -  Core Banking Maker 1",
									"linkingPreviousLimit": "",
									"linkingTimestamp": "2024-05-08T14:45:11Z"
								}
							],
							"pagination": {
								"nextCursorID": "eyJjdXN0b21lcklEIjoiNmU3OGY3YTItMTI5OS00MDRmLWI4NzQtZGM2NmQ0ODJhMWZmXzBjMWZjMDQ3LTExNjMtNDhjZS1hNDkzLTgwOGJhNGZlY2E4OSIsImV2ZW50VGltZXN0YW1wIjoiMTcxNTE1NDMxMTkzMDE0ODI5NiJ9",
								"prevCursorID": "eyJjdXN0b21lcklEIjoiNmU3OGY3YTItMTI5OS00MDRmLWI4NzQtZGM2NmQ0ODJhMWZmXzBjMWZjMDQ3LTExNjMtNDhjZS1hNDkzLTgwOGJhNGZlY2E4OSIsImV2ZW50VGltZXN0YW1wIjoiMTcyMDE0Mzc5NjI3ODI0NDczNCJ9"
							}
						},
						"meta": null,
						"safeID": "6e78f7a2-1299-404f-b874-dc66d482a1ff"
					}
				}`

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
				Expect(response.Body.String).Should(MatchJSON(expectedResponse))
			})
		})

		When("user has valid permission and getting reauth history", func() {
			It("should successfully return customer search results", func() {
				// create token
				token, err := jwt.GenerateJWTStringWithClaims(map[string]interface{}{
					"id": resources.TestCustomerSearch,
				}, service.AppConfig.TokenKey.AccessTokenExpiryTime, service.AppConfig.TokenKey.AccessTokenKey)
				if err != nil {
					log.Fatalf("an error '%s' was not expected when create jwt token", err)
				}

				xfccHeader := hcl.Header(constants.CtxAuthorization, "Bearer "+token)
				body := hcl.JSON(`{
					"identifier": "64b70551-e6a1-4ee2-8008-5f457a73e472",
					"identifierType": "SAFE_ID",
					"key": "reauthHistory"
				}`)

				mockStorage.On("GetDatabaseHandle", mock.Anything, mock.Anything).Return(db, nil)

				binaryData, _ := json.Marshal(resources.SampleDataUserPermissionsCustomerSearch())
				mockRedis.On("GetString", mock.Anything, constants.UserIDRedisKey+resources.TestCustomerSearch).Return(string(binaryData), nil)

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueriesGetUserRole)).
					WithArgs(int64(1)).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
						AddRow(1, "ADMIN"))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationDetail)).
					WithArgs("reauthHistory", 1). // keyword and status
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "parent_segregation_id",
						"order", "level", "status", "type",
					}).AddRow(
						3, "Reauthentication History", "reauthHistory", 3, 1, 1, 1, "table",
					))

				mocker.ExpectQuery(regexp.QuoteMeta(TestQueryGetSegregationByRole)).
					WillReturnRows(sqlmock.NewRows([]string{
						"id", "name", "keyword", "type", "parent_segregation_id", "order"}).
						AddRow(4, "Timestamp", "reauthTimestamp", "oneliner_datetime", 3, 1).
						AddRow(5, "Previous Mobile Number", "reauthPrevMobileNumber", "oneliner", 3, 2).
						AddRow(6, "Status", "reauthStatus", "oneliner", 3, 3))

				expectedCustJournalResponse := `{
					"links": {
						"prevCursorID": "eyJjdXN0b21lcklEIjoiNjRiNzA1NTEtZTZhMS00ZWUyLTgwMDgtNWY0NTdhNzNlNDcyIiwiZXZlbnRUaW1lc3RhbXAiOiIxNzMwMjAwODU5NzM1In0=",
						"nextCursorID": "eyJjdXN0b21lcklEIjoiNjRiNzA1NTEtZTZhMS00ZWUyLTgwMDgtNWY0NTdhNzNlNDcyIiwiZXZlbnRUaW1lc3RhbXAiOiIxNzMwMjAwNzAyMzI1In0="
					},
					"data": [
						{
							"customerID": "64b70551-e6a1-4ee2-8008-5f457a73e472",
							"eventTimestamp": "1730200859735",
							"metadata": {
								"billingAgreementID": null,
								"billingAgreementStatus": null,
								"eventType": "OVO_REAUTH",
								"newDeviceInfo": {
									"deviceBrand": null,
									"deviceID": null,
									"deviceModel": null,
									"osFamily": null,
									"osVersion": null
								},
								"newPhoneNumber": null,
								"oldDeviceInfo": {
									"deviceBrand": null,
									"deviceID": null,
									"deviceModel": null,
									"osFamily": null,
									"osVersion": null
								},
								"oldPhoneNumber": null,
								"reauthFlag": "REAUTH_SUCCESS",
								"status": "FAILED",
								"statusReason": "billingAgreementNotActive",
								"triggerEvent": null
							}
						},
						{
							"customerID": "64b70551-e6a1-4ee2-8008-5f457a73e472",
							"eventTimestamp": "1730200806885",
							"metadata": {
								"billingAgreementID": null,
								"billingAgreementStatus": null,
								"eventType": "OVO_REAUTH",
								"newDeviceInfo": {
									"deviceBrand": null,
									"deviceID": null,
									"deviceModel": null,
									"osFamily": null,
									"osVersion": null
								},
								"newPhoneNumber": null,
								"oldDeviceInfo": {
									"deviceBrand": null,
									"deviceID": null,
									"deviceModel": null,
									"osFamily": null,
									"osVersion": null
								},
								"oldPhoneNumber": null,
								"reauthFlag": "REAUTH_SUCCESS",
								"status": "FAILED",
								"statusReason": "billingAgreementNotActive",
								"triggerEvent": null
							}
						},
						{
							"customerID": "64b70551-e6a1-4ee2-8008-5f457a73e472",
							"eventTimestamp": "1730200702325",
							"metadata": {
								"billingAgreementID": null,
								"billingAgreementStatus": null,
								"eventType": "OVO_REAUTH",
								"newDeviceInfo": {
									"deviceBrand": null,
									"deviceID": null,
									"deviceModel": null,
									"osFamily": null,
									"osVersion": null
								},
								"newPhoneNumber": null,
								"oldDeviceInfo": {
									"deviceBrand": null,
									"deviceID": null,
									"deviceModel": null,
									"osFamily": null,
									"osVersion": null
								},
								"oldPhoneNumber": null,
								"reauthFlag": "REAUTH_SUCCESS",
								"status": "FAILED",
								"statusReason": "billingAgreementNotActive",
								"triggerEvent": null
							}
						}
					]
				}`

				var mockResponse *customerJournalAPI.Response
				_ = json.Unmarshal([]byte(expectedCustJournalResponse), &mockResponse)

				mockCustomerJournal.On("GetCustomerJournalData", mock.Anything, &customerJournalAPI.Request{
					UserSafeID: "64b70551-e6a1-4ee2-8008-5f457a73e472",
					PageSize:   10,
					Endpoint:   constants.CustomerJournalLogTypeOvoNabung,
				}).Return(mockResponse, nil).Once()

				expectedResponse := `{
					"status": "Success",
					"structure": {
						"key": "reauthHistory",
						"type": "table",
						"label": "Reauthentication History",
						"children": [
							{
								"key": "reauthTimestamp",
								"type": "oneliner_datetime",
								"label": "Timestamp"
							},
							{
								"key": "reauthPrevMobileNumber",
								"type": "oneliner",
								"label": "Previous Mobile Number"
							},
							{
								"key": "reauthStatus",
								"type": "oneliner",
								"label": "Status"
							}
						]
					},
					"data": {
						"cif": null,
						"meta": null,
						"reauthHistory": {
							"data": [
								{
									"reauthPrevMobileNumber": null,
									"reauthStatus": "FAILED",
									"reauthTimestamp": "1970-01-01T07:28:50Z"
								},
								{
									"reauthPrevMobileNumber": null,
									"reauthStatus": "FAILED",
									"reauthTimestamp": "1970-01-01T07:28:50Z"
								},
								{
									"reauthPrevMobileNumber": null,
									"reauthStatus": "FAILED",
									"reauthTimestamp": "1970-01-01T07:28:50Z"
								}
							],
							"pagination": {
								"nextCursorID": "eyJjdXN0b21lcklEIjoiNjRiNzA1NTEtZTZhMS00ZWUyLTgwMDgtNWY0NTdhNzNlNDcyIiwiZXZlbnRUaW1lc3RhbXAiOiIxNzMwMjAwNzAyMzI1In0=",
								"prevCursorID": "eyJjdXN0b21lcklEIjoiNjRiNzA1NTEtZTZhMS00ZWUyLTgwMDgtNWY0NTdhNzNlNDcyIiwiZXZlbnRUaW1lc3RhbXAiOiIxNzMwMjAwODU5NzM1In0="
							}
						},
						"safeID": "64b70551-e6a1-4ee2-8008-5f457a73e472"
					}
				}`

				response, err := client.Post(CustomerSearch, body, xfccHeader)

				Expect(err).ShouldNot(HaveOccurred())
				Expect(response).ShouldNot(BeNil())
				Expect(response.StatusCode).Should(Equal(200))
				Expect(response.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})
})
