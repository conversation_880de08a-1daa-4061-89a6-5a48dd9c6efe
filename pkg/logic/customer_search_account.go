package logic

import (
	"context"
	"fmt"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	accountServiceAPI "gitlab.myteksi.net/bersama/core-banking/account-service/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/logic/helper"
)

func (p *process) GetCustomerAccounts(ctx context.Context, req *api.GetCustomerAccountRequest) (*api.GetCustomerAccountsResponse, error) {
	// Validate request
	err := helper.ValidateIdentifier(string(req.IdentifierType), req.Identifier, 1)
	if err != nil {
		return nil, err
	}

	// Permission check
	_, elements, err := p.authenticateRequestByPermission(ctx, int(constants.BitwiseValueGeneralCreate))
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Unauthorized, "failed to authenticate request")
	}

	if len(elements) == 0 {
		return nil, errorwrapper.WrapError(err, apiError.Unauthorized, "no elements found with permission to access")
	}

	hasAccess := false
	for _, element := range elements {
		code := constants.ElementCodes(element.Code)
		if constants.WhitelistedSearchAccountApi[code] {
			hasAccess = true
			break
		}
	}

	if !hasAccess {
		return nil, errorwrapper.Error(apiError.Unauthorized, "no whitelisted element access granted")
	}

	// Get First Customer
	customer, customers, isLastPage, err := p.getFirstCustomer(ctx, req)
	if err != nil {
		return nil, err
	}

	cif, err := p.extractCIF(customer)
	if err != nil {
		return nil, err
	}

	// Extract SafeID
	safeID, err := p.extractSafeID(customer)
	if err != nil {
		return nil, err
	}

	// Mapping response data
	response, err := p.buildCustomerAccountResponse(ctx, req.Data, customers, cif, safeID, isLastPage)
	if err != nil {
		return nil, err
	}

	return response, nil
}

func (p *process) getFirstCustomer(ctx context.Context, req *api.GetCustomerAccountRequest) (map[string]interface{}, []interface{}, bool, error) {
	// Validate request
	search := api.GetCustomersRequest{
		Identifier:     req.Identifier,
		IdentifierType: req.IdentifierType,
		Page:           1,
	}

	customers, isLastPage, err := p.getCustomersAccount(ctx, &search)
	if err != nil {
		return nil, nil, isLastPage, err
	}

	// Ensure we have at least one customer
	customer, ok := customers[0].(map[string]interface{})
	if !ok {
		return nil, nil, isLastPage, errorwrapper.Error(apiError.InternalServerError, "invalid customer data type")
	}

	return customer, customers, isLastPage, nil
}

func (p *process) extractCIF(customer map[string]interface{}) (string, error) {
	cif, ok := customer["cif"].(string)
	if !ok {
		return "", errorwrapper.Error(apiError.InternalServerError, "invalid cif type")
	}
	return cif, nil
}

func (p *process) extractSafeID(customer map[string]interface{}) (string, error) {
	cif, ok := customer["safeID"].(string)
	if !ok {
		return "", errorwrapper.Error(apiError.InternalServerError, "invalid safeID ID")
	}
	return cif, nil
}

func (p *process) buildCustomerAccountResponse(
	ctx context.Context,
	requestedData []string,
	customers []interface{},
	cif string,
	safeID string,
	isLastPage bool,
) (*api.GetCustomerAccountsResponse, error) {
	resp := &api.GetCustomerAccountsResponse{IsLastPage: isLastPage}

	for _, item := range requestedData {
		switch item {
		case constants.ConstTypeCustomerInfo:
			resp.Customers = customers
		case constants.ConstTypeCustomerStatus:
			status, err := p.getAccessStatusCustomer(ctx, safeID)
			if err != nil {
				return nil, err
			}
			resp.CustomerStatus = *status
		case constants.ConstTypeCustomerAccounts:
			accounts, err := p.getAccounts(ctx, cif)
			if err != nil {
				return nil, err
			}
			resp.Accounts = accounts
		default:
			return nil, errorwrapper.Error(apiError.InternalServerError, fmt.Sprintf("invalid data request: %s", item))
		}
	}

	return resp, nil
}

func (p process) getCustomersAccount(ctx context.Context, req *api.GetCustomersRequest) ([]interface{}, bool, error) {
	// get customer lean
	customers, isLastPage, resultErr := p.useCustomerLean(ctx, req)
	if resultErr != nil {
		return nil, isLastPage, resultErr
	}
	if len(customers) == 0 {
		resultErr = errorwrapper.Error(apiError.ResourceNotFound, "customer not found")
		return nil, isLastPage, resultErr
	}

	return customers, isLastPage, nil
}

func (p process) getAccessStatusCustomer(ctx context.Context, safeID string) (*string, error) {
	resp, err := helper.CustomerLoginStatus(ctx, p.GrabIDClient, fmt.Sprint(safeID), constants.CustomerSearchAccountLogTag)
	if err != nil {
		return nil, err
	}

	return &resp.Status, nil
}

func (p process) getAccounts(ctx context.Context, cifNumber string) ([]interface{}, error) {
	// get accounts
	reqAccount := &accountServiceAPI.ListCASAAccountsForCustomerDetailRequest{
		CifNumber:    cifNumber,
		FetchBalance: true,
		ProductVariantIDs: []string{
			"casa_account_default",
			"casa_pocket_default",
			"pocket_ovo",
			"microsaver",
			"term_deposit_default",
		},
	}
	accountList, err := helper.GetAccountList(ctx, reqAccount, p.AccountServiceClient, constants.CustomerSearchAccountLogTag)
	if err != nil {
		return nil, err
	}
	accounts := make([]interface{}, len(accountList.Accounts))
	for i, acc := range accountList.Accounts {
		accounts[i] = acc
	}
	return accounts, nil
}
