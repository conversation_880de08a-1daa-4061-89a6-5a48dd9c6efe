package logic

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"sort"
	"strings"
	"sync"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	commonStorage "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/storage"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/validations"
	permissionManagementLogic "gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/logic"
	permissionManagementStorage "gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/logic/helper"

	customerExperienceAPI "gitlab.myteksi.net/bersama/customer-experience/api"
	customerMasterAPI "gitlab.myteksi.net/bersama/customer-master/api/v2"
	commonCtx "gitlab.myteksi.net/dakota/common/context"
	"gitlab.myteksi.net/dakota/servus/v2/slog"

	customerJournalAPI "gitlab.super-id.net/bersama/corex/customer-journal/api"
	amlServiceAPI "gitlab.super-id.net/bersama/fintrust/aml-service/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	customerExperienceHttpAPI "gitlab.super-id.net/bersama/opsce/onedash-be/external/customerexperiencehttp"
	"gitlab.super-id.net/bersama/opsce/onedash-be/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"
)

// CustomerSearch handles customer search requests with proper authorization and validation
func (p *process) CustomerSearch(ctx context.Context, req *api.CustomerSearchRequest) (*api.CustomerSearchResponse, error) {
	// check permission
	user, hasPerm, err := permissionManagementLogic.PermissionManagementProcess.AuthenticateRequestByElementCode(ctx, constants.CustomerSearch, constants.BitwiseValueGeneralRead)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Unauthorized, "failed to validate authorization")
	}
	if !hasPerm {
		return nil, errorwrapper.Error(apiError.Forbidden, "User is not authorized to perform this element action")
	}

	// validate request
	err = validations.ValidateRequest(req)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.BadRequest, "failed to validate request")
	}

	// get database handle
	db, err := commonStorage.DBStoreD.GetDatabaseHandle(ctx, p.AppConfig.Data.MySQL.Slave)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "failed to get database handle")
	}

	// get customer data
	return p.GetCustomerSearch(ctx, db, req, user)
}

// GetCustomerSearch retrieves customer data based on user roles and permissions
func (p *process) GetCustomerSearch(ctx context.Context, db *sql.DB, req *api.CustomerSearchRequest, user *permissionManagementStorage.UserDTO) (*api.CustomerSearchResponse, error) {
	var wg sync.WaitGroup
	var segregations *api.CustomerSearchStructure
	var customerData map[string]interface{}
	var segErr, custErr error

	wg.Add(2)

	go func() {
		defer wg.Done()
		segregations, segErr = p.GetSegregationByRole(ctx, db, user, req.Key)
		if segErr != nil {
			segErr = errorwrapper.WrapError(segErr, apiError.InternalServerError, "error getting related field")
		}
	}()

	go func() {
		defer wg.Done()
		customerData, custErr = p.GetCustomerData(ctx, req, user)
		if custErr != nil {
			custErr = errorwrapper.WrapError(custErr, apiError.InternalServerError, "error getting related customer data")
		}
	}()

	wg.Wait()

	if segErr != nil { // write audit trail
		return nil, segErr
	}

	if custErr != nil {
		return nil, custErr
	}

	if segregations != nil && segregations.Children != nil {
		tableFields := make([]string, 0, len(segregations.Children))

		// Only fetch second phase segregations for certain id
		for _, child := range segregations.Children {
			if constants.SelectedTableSegregations[child.Key] {
				tableFields = append(tableFields, child.Key)
			}
		}

		// Fetch table segregations in parallel
		if len(tableFields) > 0 {
			tableSegregations := make(map[string]*api.CustomerSearchStructure)
			var tableFetchWg sync.WaitGroup
			var tableFetchMu sync.Mutex

			for _, tableKey := range tableFields {
				tableFetchWg.Add(1)
				go func(key string) {
					defer tableFetchWg.Done()

					tableSeg, err := p.GetSegregationByRole(ctx, db, user, key)
					if err != nil {
						slog.FromContext(ctx).Warn(constants.CustomerSearchLogTag,
							fmt.Sprintf("failed to fetch segregation for table %s: %s", key, err.Error()),
							utils.GetTraceID(ctx))
						return
					}

					tableFetchMu.Lock()
					tableSegregations[key] = tableSeg
					tableFetchMu.Unlock()
				}(tableKey)
			}

			tableFetchWg.Wait()

			// Include table segregations into children
			for i, child := range segregations.Children {
				if tableSeg, exists := tableSegregations[child.Key]; exists && tableSeg != nil {
					segregations.Children[i].Children = tableSeg.Children
				}
			}
		}
	}

	return p.constructCustomerSearchResponse(segregations, customerData)
}

// constructCustomerSearchResponse builds the response structure based on segregation rules
func (p *process) constructCustomerSearchResponse(segregations *api.CustomerSearchStructure, customerData map[string]interface{}) (*api.CustomerSearchResponse, error) {
	if segregations == nil {
		return nil, errorwrapper.Error(apiError.BadRequest, "segregations structure cannot be nil")
	}

	// Always include global field: SafeID, cif
	result := make(map[string]interface{})
	if segregations.Type != "root" {
		result[constants.DataTypeSafeID] = customerData[constants.DataTypeSafeID]
		result[constants.DataTypeCif] = customerData[constants.DataTypeCif]
	}

	// Build valid keys map
	validKeys := buildValidKeysMap(segregations)
	// Process data based on segregation type
	if constants.ArrayTypeSegregation[segregations.Type] {
		processArrayData(segregations.Key, customerData, validKeys, result)
	} else {
		processNonArrayData(segregations, customerData, result)
	}

	// bypass meta
	result[constants.KeyMeta] = customerData[constants.KeyMeta]

	return &api.CustomerSearchResponse{
		Status:    "Success",
		Structure: segregations,
		Data:      result,
	}, nil
}

// nolint:funlen
// buildValidKeysMap creates a map of valid keys from segregation structure
func buildValidKeysMap(segregations *api.CustomerSearchStructure) map[string]interface{} {
	validKeys := make(map[string]interface{})

	if segregations.Children != nil {
		processChildrenKeys(segregations.Children, validKeys)
		childMap := buildNestedKeys(segregations.Children)
		mergeMaps(validKeys, childMap)
	}

	if segregations.Type == constants.SectionArrayType || segregations.Type == constants.TableType {
		validKeys[segregations.Key] = true

		childMap := buildNestedKeys(segregations.Children)
		mergeMaps(validKeys, childMap)
	}

	return validKeys
}

func processChildrenKeys(children []api.Structure, validKeys map[string]interface{}) {
	for i := range children {
		child := &children[i] // Get pointer to allow modification
		if child.Key != "" {
			if child.Type == constants.SectionDetailType && child.Children != nil {
				nested := extractKeys(child.Children)
				validKeys[child.Key] = nested
			} else {
				validKeys[child.Key] = true
			}
		}

		if constants.HasChildrenTypes[child.Type] {
			child.HasChildren = true
		}
	}
}

func extractKeys(children []api.Structure) map[string]interface{} {
	nested := make(map[string]interface{})
	for _, grandChild := range children {
		if grandChild.Key != "" {
			nested[grandChild.Key] = true
		}
	}
	return nested
}

func mergeMaps(dest, src map[string]interface{}) {
	for k, v := range src {
		dest[k] = v
	}
}

// buildNestedKeys ...
// nolint:gocognit
func buildNestedKeys(children []api.Structure) map[string]interface{} {
	result := make(map[string]interface{})

	for _, child := range children {
		if len(child.Children) > 0 {
			childMap := make(map[string]interface{})
			for _, grandChild := range child.Children {
				if len(grandChild.Children) > 0 {
					grandChildMap := make(map[string]bool)
					for _, ggc := range grandChild.Children {
						if ggc.Key != "" {
							grandChildMap[ggc.Key] = true
						}
					}
					if grandChild.Key != "" {
						childMap[grandChild.Key] = grandChildMap
					}
				} else if grandChild.Key != "" {
					childMap[grandChild.Key] = true
				}
			}
			if child.Key != "" {
				result[child.Key] = childMap
			}
		} else if child.Key != "" {
			result[child.Key] = true
		}
	}

	return result
}

// processArrayData handles array type data processing
// nolint:funlen,gocognit
func processArrayData(key string, customerData map[string]interface{}, validKeys map[string]interface{}, result map[string]interface{}) {
	rawArray, exists := customerData[key]
	if !exists {
		return
	}

	// Handle []map[string]interface{} or []interface{}
	var arrayData []interface{}
	var rawData map[string]interface{}

	switch v := rawArray.(type) {
	case []interface{}:
		arrayData = v
	case []map[string]interface{}:
		for _, item := range v {
			arrayData = append(arrayData, item)
		}
	//handle table with other value
	case map[string]interface{}:
		rawData = v
		if data, arrOk := v["data"].([]map[string]interface{}); arrOk {
			for _, item := range data {
				arrayData = append(arrayData, item)
			}
		}
	default:
		return // Not a valid array structure
	}

	if len(arrayData) == 0 {
		return
	}

	var tempResult []map[string]interface{}

	for _, item := range arrayData {
		customer, ok := item.(map[string]interface{})
		if !ok {
			continue
		}

		tempData := make(map[string]interface{})

		for validKey, validVal := range validKeys {
			// Skip adding the parent key to each item in the array
			if validKey == key {
				continue
			}

			switch nestedKeys := validVal.(type) {
			case bool:
				if val, ok := customer[validKey]; ok {
					tempData[validKey] = val
				} else {
					tempData[validKey] = nil
				}
			case map[string]interface{}:
				// Nested array of objects (e.g. onboardingHistorySectionDetails)
				nestedArrayRaw, ok := customer[validKey]
				if !ok {
					tempData[validKey] = []interface{}{}
					continue
				}

				var nestedArray []interface{}
				switch v := nestedArrayRaw.(type) {
				case []interface{}:
					nestedArray = v
				case []map[string]interface{}:
					for _, item := range v {
						nestedArray = append(nestedArray, item)
					}
				case map[string]interface{}:
					tempData[validKey] = v
					continue
				default:
					nestedArray = []interface{}{}
				}

				var nestedResults []map[string]interface{}
				for _, nestedItem := range nestedArray {
					nestedMap, ok := nestedItem.(map[string]interface{})
					if !ok {
						continue
					}
					nestedResult := make(map[string]interface{})
					for nestedKey := range nestedKeys {
						if val, ok := nestedMap[nestedKey]; ok {
							nestedResult[nestedKey] = val
						} else {
							nestedResult[nestedKey] = nil
						}
					}
					nestedResults = append(nestedResults, nestedResult)
				}

				tempData[validKey] = nestedResults
			}
		}

		tempResult = append(tempResult, tempData)
	}

	if constants.TableDataWithInfo[key] {
		rawData["data"] = tempResult
		result[key] = rawData
	} else {
		result[key] = tempResult
	}
}

// processNonArrayData handles non-array type data processing
// nolint:gocognit
func processNonArrayData(segregations *api.CustomerSearchStructure, customerData map[string]interface{}, result map[string]interface{}) {
	if segregations.Children == nil {
		return
	}

	for _, child := range segregations.Children {
		if child.Type != constants.TableType && child.Type != constants.SectionArrayType {
			// Regular field
			if value, exists := customerData[child.Key]; exists {
				result[child.Key] = value
			}
		} else if tableData, exists := customerData[child.Key]; exists {
			// Table or section array field
			if len(child.Children) > 0 {
				// Build valid keys map for table columns
				tableValidKeys := make(map[string]bool)
				for _, tableChild := range child.Children {
					tableValidKeys[tableChild.Key] = true
				}

				// Process table data
				if tableArray, ok := tableData.([]map[string]interface{}); ok {
					filteredTableData := validateArrayItems(tableArray, tableValidKeys)
					if len(filteredTableData) > 0 {
						result[child.Key] = filteredTableData
					}
				}
			} else {
				// No column segregation data, include all
				result[child.Key] = tableData
			}
		}
	}

	// Custom Child Segregation for active and inactive term loan list
	if value, exist := customerData[constants.KeyActiveLoanDetail]; exist {
		result[constants.KeyActiveLoanDetail] = value
	}

	if value, exist := customerData[constants.KeyInactiveLoanDetail]; exist {
		result[constants.KeyInactiveLoanDetail] = value
	}
}

// validateArrayItems validates individual items in an array
func validateArrayItems(dataArray []map[string]interface{}, validKeys map[string]bool) []map[string]interface{} {
	validatedArray := make([]map[string]interface{}, 0, len(dataArray))

	for _, item := range dataArray {
		validatedItem := make(map[string]interface{})

		for key, value := range item {
			if validKeys[key] {
				validatedItem[key] = value
			}
		}

		if len(validatedItem) > 0 {
			validatedArray = append(validatedArray, validatedItem)
		}
	}

	return validatedArray
}

func (p *process) GetCustomerData(ctx context.Context, req *api.CustomerSearchRequest, user *permissionManagementStorage.UserDTO) (map[string]interface{}, error) {
	if constants.KeyGroups.CustomerLean[req.Key] {
		return p.GetCustomerSafeID(ctx, req.Identifier, string(req.IdentifierType), req.Key)
	}

	if constants.KeyGroups.CustomerOps[req.Key] {
		_, err := p.GetCustomerByIdentifier(ctx, req.Identifier, string(req.IdentifierType), 1)
		resp, err := p.GetCustomerByIdentifier(ctx, req.Identifier, string(req.IdentifierType), 1)
		if err != nil {
			return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "error getting customer related data by identifier")
		}
		var respAml *amlServiceAPI.GetCustomerResponse
		if constants.KeyGroups.AmlData[req.Key] {
			respAml, _ = p.GetCustomerAmlService(ctx, req.Identifier, req.Key)
		}
		var customerPortal *helper.EventLogResponse
		if req.Key == constants.KeyOnboardingDetails {
			customerPortal, _ = p.GetCustomerJournalSafeID(ctx, req.Identifier, 1, map[string]string{}, constants.CustomerJournalLogTypeQCFlag)
		}
		return p.constructCustomerRelatedData(ctx, resp, req.Key, respAml, customerPortal), nil
	}

	if constants.KeyGroups.FundingTab[req.Key] {
		return p.mappingFundingRelatedData(ctx, req, user)
	}

	if constants.KeyGroups.TransactionsTab[req.Key] {
		return p.mappingTransactionData(ctx, req)
	}

	if constants.KeyGroups.AppActivity[req.Key] {
		return p.mappingEventHistoryRelatedData(ctx, req)
	}

	if constants.KeyGroups.LendingTab[req.Key] {
		return p.mappingLendingRelatedData(ctx, req)
	}

	if constants.KeyGroups.Summary[req.Key] {
		return p.mappingCustomerSummary(ctx, req)
	}

	if constants.KeyGroups.OnboardingHistory[req.Key] {
		return p.mappingOnboardingHistory(ctx, req.Identifier, string(req.IdentifierType), req.Key)
	}

	if constants.KeyGroups.BankInitiateActivity[req.Key] {
		return p.mappingBankInitiatedActivity(ctx, req)
	}

	return nil, nil
}

func (p *process) GetCustomerJournalSafeID(ctx context.Context, safeID string, pageSize int32, payload map[string]string, endpoint string) (*helper.EventLogResponse, error) {
	request := &customerJournalAPI.Request{
		UserSafeID:     safeID,
		PageSize:       pageSize,
		StartDate:      payload["startDate"],
		EndDate:        payload["endDate"],
		StartingBefore: payload["startingBefore"],
		EndingAfter:    payload["endingAfter"],
		Endpoint:       endpoint,
	}
	resp, err := helper.GetEventLog(ctx, request, constants.CustomerSearchLogTag, p.CustomerJournalClient.GetCustomerJournalData)

	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.BadRequest, "failed to customer search request")
	}
	return resp, nil
}

func (p *process) GetCustomerSafeID(ctx context.Context, identifier, identifierType, key string) (map[string]interface{}, error) {
	result := make(map[string]interface{})

	// Determine fields to fetch
	keyToType := map[string]string{
		constants.KeyAddressDetails: constants.AddressesType,
		constants.KeyContactDetails: constants.ContactsType,
	}

	fields := []string{constants.PersonalInfoType}
	if fetchType, exists := keyToType[key]; exists {
		fields = append(fields, fetchType)
	}

	// Get customer data
	customerData, err := p.getCustomerData(ctx, identifier, identifierType, fields)
	if err != nil {
		return nil, err
	}

	// Handle global data: SafeID, CIF
	result[constants.DataTypeSafeID] = ""
	result[constants.DataTypeCif] = ""

	mandatoryField := constants.MappingGlobalDataCustomerLeanKey[constants.DataTypeSafeID]
	if safeID, exists := customerData[mandatoryField]; exists {
		result[constants.DataTypeSafeID] = safeID
		result[constants.DataTypeCif] = customerData[constants.MappingGlobalDataCustomerLeanKey[constants.DataTypeCif]]
	}

	// Process specific data types
	if err := p.processDataByKey(key, customerData, result); err != nil {
		return result, err
	}

	if key == constants.KeyAddressDetails {
		if addresses, ok := result[constants.KeyAddressDetails]; ok && !isEmptyAddresses(addresses) {
			return result, nil
		}

		// Fallback to CustomerOps
		opsResp, err := p.GetCustomerByIdentifier(ctx, identifier, identifierType, 1)
		if err != nil {
			return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "error getting customer address from ops")
		}
		var respAml *amlServiceAPI.GetCustomerResponse
		var customerPortal *helper.EventLogResponse
		if len(opsResp.Items) > 0 {
			// Use the same fieldMapping for CustomerOps data
			return p.constructCustomerRelatedData(ctx, opsResp, key, respAml, customerPortal), nil
		}
	}

	return result, nil
}

func isEmptyAddresses(addresses interface{}) bool {
	switch v := addresses.(type) {
	case []interface{}:
		return len(v) == 0
	case []map[string]interface{}:
		return len(v) == 0
	default:
		return true
	}
}

func (p *process) getCustomerData(ctx context.Context, identifier, identifierType string, fields []string) (map[string]interface{}, error) {
	data, err := p.GetCustomerLean(ctx, identifier, identifierType, fields)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "error getting customer lean")
	}

	var customerData map[string]interface{}
	customerJSON, err := json.Marshal(data.Customer.Data)
	if err != nil {
		slog.FromContext(ctx).Error(constants.CustomerSearchLogTag, "Unable to marshall customer data", utils.GetTraceID(ctx))
		return nil, api.DefaultInternalServerError
	}

	if err := json.Unmarshal(customerJSON, &customerData); err != nil {
		slog.FromContext(ctx).Error(constants.CustomerSearchLogTag, "Unable to unmarshall customer data", utils.GetTraceID(ctx))
		return nil, api.DefaultInternalServerError
	}

	return customerData, nil
}

func (p *process) processDataByKey(key string, customerData, result map[string]interface{}) error {
	if mappings, exists := constants.FieldMappings[key]; exists {
		switch key {
		case constants.KeyAddressDetails:
			return processArrayDetails(
				customerData,
				result,
				constants.AddressesType,
				constants.KeyAddressDetails,
				mappings,
			)
		case constants.KeyContactDetails:
			return processArrayDetails(
				customerData,
				result,
				constants.ContactsType,
				constants.KeyContactDetails,
				mappings,
			)
		}
	}
	return nil
}

// processArrayDetails is a generic helper function to process array-type details
// nolint:gocognit
func processArrayDetails(sourceData map[string]interface{}, result map[string]interface{}, sourceKey string, targetKey string, mappings map[string]string) error {
	if details, exists := sourceData[sourceKey]; exists {
		detailsList, ok := details.([]interface{})
		if !ok {
			return nil
		}

		validatedDetails := make([]map[string]interface{}, 0, len(detailsList))
		for _, detail := range detailsList {
			detailMap, ok := detail.(map[string]interface{})
			if !ok {
				continue
			}

			// Skip employment addresses if processing address details
			if sourceKey == constants.AddressesType {
				if addressType, exists := detailMap["addressType"]; exists {
					if typeStr, ok := addressType.(string); ok && strings.EqualFold(typeStr, "employment") {
						continue
					}
				}
			}

			validatedDetail := mapFields(detailMap, mappings)
			if len(validatedDetail) > 0 {
				validatedDetails = append(validatedDetails, validatedDetail)
			}
		}

		if len(validatedDetails) > 0 {
			result[targetKey] = validatedDetails
		}
	}
	return nil
}

func mapFields(data map[string]interface{}, mappings map[string]string) map[string]interface{} {
	result := make(map[string]interface{})
	for inputKey, outputKey := range mappings {
		if value, exists := data[inputKey]; exists {
			result[outputKey] = value
		}
	}
	return result
}

// GetCustomerLean ...
func (p *process) GetCustomerLean(ctx context.Context, identifier string, identifierType string, dataFetchedType []string) (*customerMasterAPI.GetCustomerByIdentifierResponse, error) {
	customerReq := &customerMasterAPI.GetCustomerLeanRequest{
		Identifier:     identifier,
		IdentifierType: customerMasterAPI.IdentifierType(identifierType),
		Data:           make([]customerMasterAPI.DataFetchedType, 0),
		ServiceID:      constants.DIGIBANK,
	}

	for _, dataType := range dataFetchedType {
		customerReq.Data = append(customerReq.Data, customerMasterAPI.DataFetchedType(dataType))
	}

	customerRes, err := p.CustomerMasterClient.GetCustomerLean(ctx, customerReq)
	if err != nil {
		slog.FromContext(ctx).Error(constants.CustomerSearchLogTag, constants.CustomerMasterLogErrorPrefix+err.Error(), utils.GetTraceID(ctx))
		return nil, err
	}
	return customerRes, nil
}

func (p process) constructCustomerRelatedData(ctx context.Context, resp *customerExperienceAPI.GetCustomerOpsResponse, key string, respAml *amlServiceAPI.GetCustomerResponse, respCustomerPortal *helper.EventLogResponse) map[string]interface{} {
	// Preallocate maps with expected capacity
	data := make(map[string]interface{})

	if resp == nil || len(resp.Items) == 0 {
		return data
	}

	customerInfo := resp.Items[0].Customer

	setBasicCustomerInfo(data, &customerInfo, &customerInfo.Employments, respAml)

	switch key {
	case constants.KeyAddressDetails:
		setAddressDetails(data, &customerInfo.Addresses)
	case constants.KeyBeneficiaryOwnerDetails:
		setBeneficiaryDetails(data, respAml)
	case constants.KeyOccupationDetails:
		setOccupationDetails(data, &customerInfo.Employments)
	case constants.KeyOnboardingDetails, constants.KeyOnboardingDetailsVerificationDetails:
		onboardingInfo := resp.Items[0].Applications
		if len(onboardingInfo) == 0 {
			return data
		}
		setOnboardingDetails(data, &onboardingInfo[0], resp.Items[0].OnboardingAttemptTime, respCustomerPortal)
	case constants.KeyChannelAccess:
		p.setChannelAccessDetails(ctx, data)
	case constants.KeyCustomerEnhancedDueDiligence:
		setCustomerEnhancedDueDiligence(data, respAml)
	case constants.KeyCustomerReEnhancedDueDiligence:
		setCustomerReEnhancedDueDiligence(data, respAml)
	}

	return data
}

func (p process) setChannelAccessDetails(ctx context.Context, data map[string]interface{}) {
	if safeID, exists := data[constants.KeyCustomerDetailsSafeID]; exists {
		resp, err := helper.CustomerLoginStatus(ctx, p.GrabIDClient, fmt.Sprint(safeID), constants.CustomerSearchLogTag)
		if err != nil {
			return
		}
		if resp != nil {
			data[constants.KeyChannelAccessCustomerAccessStatus] = resp.Status
		}
	}
}

func setAddressDetails(data map[string]interface{}, addresses *[]customerExperienceAPI.Addresses) {
	if addresses == nil || len(*addresses) == 0 {
		return
	}

	addressList := make([]map[string]interface{}, 0, len(*addresses))

	for _, addr := range *addresses {
		// Skip employment addresses if processing address details
		if strings.EqualFold(string(addr.AddressType), "employment") {
			continue
		}

		mappedAddr := map[string]interface{}{
			"addressType": addr.AddressType,
			"city":        addr.City,
			"country":     addr.Country,
			"postalCode":  addr.PostalCode,
			"province":    addr.Province,
			"rt":          addr.Rt,
			"rw":          addr.Rw,
			"street":      addr.Street,
			"subdistrict": addr.Subdistrict,
			"village":     addr.Village,
		}

		transformedAddr := mapFields(mappedAddr, constants.FieldMappings[constants.KeyAddressDetails])
		if len(transformedAddr) > 0 {
			addressList = append(addressList, transformedAddr)
		}
	}

	if len(addressList) > 0 {
		data[constants.KeyAddressDetails] = addressList
	}
}

// nolint: funlen
func setOnboardingDetails(data map[string]interface{}, onboardingInfo *customerExperienceAPI.Applications, onboardingAttemptTime int, eventLogResponse *helper.EventLogResponse) {
	var acceptedConsent, unacceptedConsent []string
	for _, v := range onboardingInfo.Verifications.Consents {
		if v.Status == "ACCEPTED" {
			acceptedConsent = append(acceptedConsent, v.AgreementName)
		} else {
			unacceptedConsent = append(unacceptedConsent, v.AgreementName)
		}
	}

	var ktpFile, selfieFile string
	if len(onboardingInfo.KtpFile) > 0 {
		ktpFile = onboardingInfo.KtpFile[0].URL
	}
	if len(onboardingInfo.SelfieFile) > 0 {
		selfieFile = onboardingInfo.SelfieFile[0].URL
	}
	var metadata map[string]interface{}
	if eventLogResponse != nil && eventLogResponse.Data != nil {
		if dataSlice, ok := eventLogResponse.Data.([]interface{}); ok && len(dataSlice) > 0 {
			if dataMap, ok := dataSlice[0].(map[string]interface{}); ok {
				if convMetadata, ok := dataMap["metadata"].(map[string]interface{}); ok {
					metadata = convMetadata
				}
			}
		}
	}

	details := map[string]interface{}{
		constants.KeyOnboardingDetailsKtpFile:                   ktpFile,
		constants.KeyOnboardingDetailsSelfieFile:                selfieFile,
		constants.KeyOnboardingDetailsApplicationStatus:         onboardingInfo.Status,
		constants.KeyOnboardingDetailsStatusRemarks:             onboardingInfo.StatusRemarks,
		constants.KeyOnboardingDetailsChannel:                   onboardingInfo.Channel,
		constants.KeyOnboardingDetailsApplicationID:             onboardingInfo.ID,
		constants.KeyOnboardingDetailsReferralCode:              onboardingInfo.Verifications.ReferralCode,
		constants.KeyOnboardingDetailsAttemptTime:               onboardingAttemptTime,
		constants.KeyOnboardingDetailsQCFlag:                    metadata["qcResult"],
		constants.KeyOnboardingDetailsLivenessAttemptTime:       onboardingInfo.Verifications.LivenessAttemptTime,
		constants.KeyOnboardingDetailsOcrAttemptTime:            onboardingInfo.Verifications.OCRAttemptTime,
		constants.KeyOnboardingDetailsSubmittedAt:               onboardingInfo.SubmittedAt,
		constants.KeyOnboardingDetailsCreatedAt:                 onboardingInfo.CreatedAt,
		constants.KeyOnboardingDetailsApprovedAt:                onboardingInfo.ApprovedAt,
		constants.KeyOnboardingDetailsUpdatedAt:                 onboardingInfo.UpdatedAt,
		constants.KeyOnboardingDetailsExpiresAt:                 onboardingInfo.ExpiresAt,
		constants.KeyOnboardingDetailsAMLCheck:                  onboardingInfo.Verifications.AMLCheck,
		constants.KeyOnboardingDetailsDuplicateCheckNIK:         onboardingInfo.Verifications.DuplicateCheckNIK,
		constants.KeyOnboardingDetailsDukcapilFacialMatch:       onboardingInfo.Verifications.DukcapilFacialMatch,
		constants.KeyOnboardingDetailsDukcapilDataMatchNIK:      onboardingInfo.Verifications.DukcapilDataMatchNIK.Status,
		constants.KeyOnboardingDetailsDukcapilDataMatchFullName: onboardingInfo.Verifications.DukcapilDataMatchFullName.Status,
		constants.KeyOnboardingDetailsDukcapilDataMatchDOB:      onboardingInfo.Verifications.DukcapilDataMatchDOB.Status,
		constants.KeyOnboardingDetailsDukcapilDataMatchSelfie:   onboardingInfo.Verifications.DukcapilDataMatchSelfie.Status,
		constants.KeyOnboardingDetailsDukcapilMotherMaidenName:  onboardingInfo.Verifications.DukcapilDataMatchMotherMaidenName.Status,
		constants.KeyOnboardingDetailsAcceptedConsent:           acceptedConsent,
		constants.KeyOnboardingDetailsUnacceptedConsent:         unacceptedConsent,
		constants.KeyOnboardingDetailsLivenessCheck:             onboardingInfo.Verifications.LivenessCheck,
		constants.KeyOnboardingDetailsRiskRatingCheck:           onboardingInfo.Verifications.RiskRatingCheck,
	}

	for k, v := range details {
		data[k] = v
	}
}

func setBasicCustomerInfo(data map[string]interface{}, customerInfo *customerExperienceAPI.CustomerRelatedData, employment *customerExperienceAPI.Employments, customerAml *amlServiceAPI.GetCustomerResponse) {
	basicInfo := map[string]interface{}{
		constants.KeyCustomerDetailsFullName:                         customerInfo.FullName,
		constants.KeyCustomerDetailsAlias:                            customerInfo.Alias,
		constants.KeyCustomerDetailsSafeID:                           customerInfo.ID,
		constants.KeyCustomerDetailsCif:                              customerInfo.CIF,
		constants.KeyCustomerDetailsNik:                              customerInfo.NIK,
		constants.KeyCustomerDetailsGender:                           customerInfo.Gender,
		constants.KeyCustomerDetailsDateOfBirth:                      customerInfo.DateOfBirth,
		constants.KeyCustomerDetailsPlaceOfBirth:                     customerInfo.PlaceOfBirth,
		constants.KeyCustomerDetailsNationality:                      customerInfo.Nationality,
		constants.KeyCustomerDetailsStatus:                           customerInfo.Status,
		constants.KeyCustomerDetailsIsBO:                             strings.ToUpper(fmt.Sprintf("%v", customerInfo.IsBO)),
		constants.KeyCustomerDetailsIsECDD:                           customerInfo.IsECDD,
		constants.KeyCustomerDetailsStartDate:                        customerInfo.StartDate,
		constants.KeyCustomerDetailsMotherMaidenName:                 customerInfo.MotherMaidenName,
		constants.KeyCustomerDetailsMaritalStatus:                    customerInfo.MaritalStatus,
		constants.KeyCustomerDetailsMonthlyIncome:                    employment.MonthlyIncome,
		constants.KeyCustomerDetailsAmlOngoingScreening:              "",
		constants.KeyCustomerDetailsLatestRiskRating:                 "",
		constants.KeyCustomerDetailsSourceOfFunds:                    employment.SourceOfFunds,
		constants.KeyCustomerDetailsPurposeOfAccount:                 employment.PurposeOfAccount,
		constants.KeyCustomerDetailsTotalCasaAccountRemainingBalance: "",
	}

	if customerAml != nil && customerAml.Data.NameScreening != nil {
		basicInfo[constants.KeyCustomerDetailsAmlOngoingScreening] = customerAml.Data.NameScreening.WatchlistStatus
	}
	if customerAml != nil && customerAml.Data.RiskRating != nil {
		basicInfo[constants.KeyCustomerDetailsLatestRiskRating] = customerAml.Data.RiskRating.Rating
	}

	for k, v := range basicInfo {
		data[k] = v
	}
}

func setBeneficiaryDetails(data map[string]interface{}, amlData *amlServiceAPI.GetCustomerResponse) {
	if amlData == nil || amlData.Data.Bo == nil {
		return
	}

	beneficiary := amlData.Data.Bo
	details := map[string]interface{}{
		constants.KeyBeneficiaryOwnerDetailsFullName:    beneficiary.FullName,
		constants.KeyBeneficiaryOwnerDetailsNik:         beneficiary.Nik,
		constants.KeyBeneficiaryOwnerDetailsDateOfBirth: beneficiary.DateOfBirth,
		constants.KeyBeneficiaryOwnerDetailsCountry:     beneficiary.Country,
		constants.KeyBeneficiaryOwnerDetailsOccupation:  beneficiary.Occupation,
		constants.KeyBeneficiaryOwnerDetailsIndustry:    beneficiary.Industry,
		constants.KeyBeneficiaryOwnerDetailsMonthIncome: beneficiary.MonthlyIncome,
		constants.KeyBeneficiaryOwnerDetailsPassport:    beneficiary.Passport,
		constants.KeyBeneficiaryOwnerDetailsPosition:    beneficiary.JobPosition,
		constants.KeyBeneficiaryOwnerDetailsDomicile:    beneficiary.Province,
		constants.KeyBeneficiaryOwnerDetailsNationality: beneficiary.Nationality,
	}

	// Only add non-nil values to the data map
	for k, v := range details {
		data[k] = v
	}
}

func setOccupationDetails(data map[string]interface{}, employment *customerExperienceAPI.Employments) map[string]interface{} {
	details := map[string]interface{}{
		constants.KeyOccupationDetailsEmployerName:      employment.EmployerName,
		constants.KeyOccupationDetailsOccupation:        employment.Occupation,
		constants.KeyOccupationDetailsJobPosition:       employment.JobPosition,
		constants.KeyOccupationDetailsIndustrySector:    employment.IndustrySector,
		constants.KeyOccupationDetailsEmploymentAddress: employment.Address,
		constants.KeyOccupationDetailsWorkPhoneNumber:   employment.WorkPhoneNumber,
		constants.KeyOccupationDetailsNPWP:              employment.NPWP,
	}

	for k, v := range details {
		data[k] = v
	}

	return data
}

func setCustomerEnhancedDueDiligence(data map[string]interface{}, amlData *amlServiceAPI.GetCustomerResponse) {
	if amlData == nil || amlData.Data.Ecdd == nil {
		return
	}

	ecdd := amlData.Data.Ecdd

	orderPriority := map[string]int{
		constants.CustomerAmlServiceAuthorityFigure:                 1,
		constants.CustomerAmlServiceAlternativeIncome:               2,
		constants.CustomerAmlServiceAlternativeSourceOfIncome:       3,
		constants.CustomerAmlServiceMonthlyAmountOfIncome:           4,
		constants.CustomerAmlServiceMonthlyNumberOfTransaction:      5,
		constants.CustomerAmlServiceWealthOwnership:                 6,
		constants.CustomerAmlServiceAlternativeYearlyAmountOfIncome: 7,
	}

	type orderedItem struct {
		item  amlServiceAPI.ECDDAnswer
		order int
	}

	var filteredItems []orderedItem

	for _, item := range ecdd.Data {
		if order, exists := orderPriority[item.QuestionID]; exists {
			filteredItems = append(filteredItems, orderedItem{
				item:  item,
				order: order,
			})
		}
	}

	sort.Slice(filteredItems, func(i, j int) bool {
		return filteredItems[i].order < filteredItems[j].order
	})

	result := make([]map[string]any, 0, len(filteredItems))
	for _, ordered := range filteredItems {
		result = append(result, map[string]any{
			constants.KeyQuestion: ordered.item.QuestionDescription,
			constants.KeyAnswer:   ordered.item.AnswerDescription,
		})
	}

	data[constants.KeyCustomerEnhancedDueDiligence] = map[string]interface{}{
		"data": result,
	}
}

func setCustomerReEnhancedDueDiligence(data map[string]interface{}, customerData *amlServiceAPI.GetCustomerResponse) {
	if customerData == nil || customerData.Data == nil {
		return
	}

	var isReCDD interface{}
	var latestDate interface{}
	var nextDate interface{}

	if customerData.Data.CustomerFlag != nil {
		if customerData.Data.CustomerFlag.IsReCDD {
			isReCDD = constants.KeyTrue
		} else {
			isReCDD = constants.KeyFalse
		}
	}

	if customerData.Data.Recdd != nil {
		latestDate = customerData.Data.Recdd.LatestReCDDDate
		nextDate = customerData.Data.Recdd.NextReCDDDate
	}

	details := map[string]interface{}{
		constants.KeyCustomerReEnhancedDueDiligenceFlag:       isReCDD,
		constants.KeyCustomerReEnhancedDueDiligenceLatestDate: latestDate,
		constants.KeyCustomerReEnhancedDueDiligenceNextDate:   nextDate,
	}

	for k, v := range details {
		data[k] = v
	}
}

// GetCustomerByIdentifier ...
func (p process) GetCustomerByIdentifier(ctx context.Context, ID string, identifierType string, page int) (*customerExperienceAPI.GetCustomerOpsResponse, error) {
	customerReq := &customerExperienceAPI.GetCustomerOpsRequest{
		Identifier:     ID,
		IdentifierType: identifierType,
		Page:           page,
	}
	withHTTPHeader := commonCtx.WithHTTPHeader(ctx, commonCtx.HeaderXServiceID, constants.DIGIBANK)

	customerRes, err := p.CustomerExperienceClient.GetCustomerOps(withHTTPHeader, customerReq)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.CustomerSearchLogTag, constants.CustomerExperienceLogErrorPrefix+err.Error(), utils.GetTraceID(ctx))
		return nil, err
	}
	return customerRes, nil
}

// GetCustomerAmlService ...
func (p *process) GetCustomerAmlService(ctx context.Context, ID string, key string) (*amlServiceAPI.GetCustomerResponse, error) {
	var payloadData []string
	switch key {
	case constants.KeyCustomerReEnhancedDueDiligence:
		payloadData = []string{
			constants.CustomerAmlServiceDataTypeRecdd,
			constants.CustomerAmlServiceDataTypeCustomerFlag,
		}
	default:
		payloadData = []string{
			constants.CustomerAmlServiceDataTypeNameScreening,
			constants.CustomerAmlServiceDataTypeRiskRating,
			constants.CustomerAmlServiceDataTypeBo,
			constants.CustomerAmlServiceDataTypeEcdd,
		}
	}

	customerReq := &amlServiceAPI.GetCustomerRequest{
		Identifier: ID,
		Data:       payloadData,
	}
	resp, err := helper.GetCustomerData(ctx, customerReq, constants.AmlServiceLogErrorPrefix, p.AmlServiceCustomerClient)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.CustomerSearchLogTag, constants.AmlServiceLogErrorPrefix+err.Error(), utils.GetTraceID(ctx))
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "error getting customer data from AML service")
	}
	return resp, nil
}

// GetSegregationByRole ...
// nolint: gocognit, funlen
func (p *process) GetSegregationByRole(ctx context.Context, db *sql.DB, users *permissionManagementStorage.UserDTO, key string) (*api.CustomerSearchStructure, error) {
	// get user roles
	roles, err := permissionManagementStorage.GetUserRole(ctx, db, users.ID)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "error getting user roles")
	}

	// extract role IDs
	roleIDs := make([]int64, len(roles))
	for i, role := range roles {
		roleIDs[i] = role.ID
	}

	// check if key exists
	var segFilters []commonStorage.QueryCondition
	segFilters = append(segFilters, commonStorage.EqualTo("s.keyword", key))
	segFilters = append(segFilters, commonStorage.EqualTo("s.status", 1))
	segregation, err := storage.GetSegregationDetails(ctx, db, segFilters)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "error getting segregation details")
	}

	if segregation == nil {
		if key != "" {
			return nil, errorwrapper.Error(apiError.FieldInvalid, "invalid key")
		}
		segregation = &storage.SegregationDTO{
			Key:  "",
			Type: "root",
			Name: "",
		}
	}

	// construct query condition
	filters := constructGetSegregationByRoleFilters(key, roleIDs, segregation)

	// get segregation based on role
	segregations, err := storage.GetSegregationByRole(ctx, db, filters)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "error getting related field by permission")
	}

	// construct segregations result
	var segResult api.CustomerSearchStructure

	var children []api.Structure

	for _, seg := range segregations {
		// Skip non-root nodes if key is empty
		if key == "" && seg.ParentSegregationID.Valid && seg.ParentSegregationID.Int64 != 0 {
			continue
		}

		// Build base structure
		structure := api.Structure{
			Key:   seg.Key,
			Type:  seg.Type,
			Label: seg.Name,
		}

		var childItems []api.Structure

		switch seg.Type {
		case constants.SectionDetailType, constants.SectionDetailTableType:
			// Load and attach children for detail type
			filter := constructGetSegregationByRoleFilters(key, roleIDs, seg)
			segDetails, err := storage.GetSegregationByRole(ctx, db, filter)
			if err != nil {
				return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "error getting related field by permission")
			}

			for _, child := range segDetails {
				if child.ParentSegregationID.Valid && child.ParentSegregationID.Int64 == seg.ID {
					var grandChildItems []api.Structure

					if child.Type == constants.SectionDetailTableType {
						// Load and attach children for detail type
						filter := constructGetSegregationByRoleFilters(child.Key, roleIDs, child)
						grandChildSegDetails, err := storage.GetSegregationByRole(ctx, db, filter)
						if err != nil {
							return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "error getting related field by permission")
						}

						for _, grandChild := range grandChildSegDetails {
							if grandChild.ParentSegregationID.Valid && grandChild.ParentSegregationID.Int64 == child.ID {
								grandChildItems = append(grandChildItems, api.Structure{
									Key:   grandChild.Key,
									Type:  grandChild.Type,
									Label: grandChild.Name,
								})
							}
						}
					}

					childItems = append(childItems, api.Structure{
						Key:      child.Key,
						Type:     child.Type,
						Label:    child.Name,
						Children: grandChildItems,
					})
				}
			}

		default:
			// Attach immediate children if root
			if key == "" {
				for _, child := range segregations {
					if child.ParentSegregationID.Valid && child.ParentSegregationID.Int64 == seg.ID {
						childItems = append(childItems, api.Structure{
							Key:   child.Key,
							Type:  child.Type,
							Label: child.Name,
						})
					}
				}
			}
		}

		structure.Children = childItems
		children = append(children, structure)
	}

	if segregation.Type == constants.SectionQnA {
		children = append(children,
			api.Structure{
				Key:   constants.KeyQuestion,
				Type:  constants.OnelinerType,
				Label: constants.LabelQuestion,
			},
			api.Structure{
				Key:   constants.KeyAnswer,
				Type:  constants.OnelinerType,
				Label: constants.LabelAnswer,
			},
		)
	}

	segResult.Key = segregation.Key
	segResult.Label = segregation.Name
	segResult.Type = segregation.Type
	segResult.Children = children

	return &segResult, nil
}

// constructGetSegregationByRoleFilters ...
func constructGetSegregationByRoleFilters(key string, roleIDs []int64, segregation *storage.SegregationDTO) []commonStorage.QueryCondition {
	var filters []commonStorage.QueryCondition
	roleIDAny := utils.ConvertArrayIntToAny(roleIDs)
	if len(roleIDs) != 0 {
		filters = append(filters, commonStorage.In("rs.role_id", roleIDAny...))
	}

	filters = append(filters, commonStorage.AscendingOrder("s.order"))
	filters = append(filters, commonStorage.EqualTo("s.status", 1))

	if key == "" {
		levelFilters := []commonStorage.QueryCondition{
			commonStorage.EqualTo("s.level", 1),
			commonStorage.And(
				commonStorage.EqualTo("s.level", 2),
				commonStorage.Or(
					commonStorage.EqualTo("s.type", constants.DropdownType),
					commonStorage.EqualTo("s.type", constants.SubTabType),
				),
			),
		}
		filters = append(filters, commonStorage.Or(levelFilters...))
	} else {
		filters = append(filters, commonStorage.And(
			commonStorage.EqualTo("s.parent_segregation_id", segregation.ID),
			commonStorage.NotEqualTo("s.type", constants.DropdownType),
		))
	}

	return filters
}

// GetCustomerVideoCallTickets ...
func (p process) GetCustomerVideoCallTickets(ctx context.Context, applicationID string, userID string) (*[]customerExperienceHttpAPI.VideoCallTicket, error) {
	// Call video call client to get tickets
	response, err := p.CustomerExperienceHttpClient.GetVideoCallOps(ctx, applicationID, userID)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.CustomerSearchLogTag, constants.VideoCallLogErrorPrefix+err.Error(), utils.GetTraceID(ctx))
		return nil, err
	}

	return &response.Items, nil
}

func attachMeta(result map[string]any, data map[string]any) {
	result[constants.KeyMeta] = data
}
