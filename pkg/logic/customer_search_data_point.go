package logic

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/validations"
	productMasterAPI "gitlab.myteksi.net/bersama/core-banking/product-master/api"
	txHistoryAPI "gitlab.myteksi.net/bersama/transaction-history/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/db/redis"
	permissionManagementLogic "gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/logic"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/logic/helper"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"
)

// CustomerSearchDataPoint handles request for specific data point in customer search
func (p *process) CustomerSearchDataPoint(ctx context.Context, req *api.CustomerSearchDataPointRequest) (*api.CustomerSearchDataPointResponse, error) {
	// check permission
	_, hasPerm, err := permissionManagementLogic.PermissionManagementProcess.AuthenticateRequestByElementCode(ctx, constants.CustomerSearch, constants.BitwiseValueGeneralRead)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.Unauthorized, "failed to validate authorization")
	}
	if !hasPerm {
		return nil, errorwrapper.Error(apiError.Forbidden, "User is not authorized to perform this element action")
	}

	// validate request
	err = validations.ValidateRequest(req)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.BadRequest, "failed to validate request")
	}

	return p.GetDataPoints(ctx, req)
}

// GetDataPoints ...
func (p *process) GetDataPoints(ctx context.Context, req *api.CustomerSearchDataPointRequest) (*api.CustomerSearchDataPointResponse, error) {
	if constants.MappingKeyGroupDataPoint.Funding[req.Key] {
		return p.mappingFundingDataPoints(ctx, req)
	}
	return nil, nil
}

// data points only for in funding tab
func (p *process) mappingFundingDataPoints(ctx context.Context, req *api.CustomerSearchDataPointRequest) (*api.CustomerSearchDataPointResponse, error) {
	switch req.Key {
	case constants.KeyPiggybankGoalAmount:
		return p.getPiggybankGoalAmount(ctx)
	case constants.KeyPiggybankLastSuccessDeduction:
		return p.getPiggybankLastSuccessDeduction(ctx, req)
	}
	return nil, nil
}

// getPiggybankGoalAmount ...
func (p *process) getPiggybankGoalAmount(ctx context.Context) (*api.CustomerSearchDataPointResponse, error) {
	redisKey := fmt.Sprintf("%s_%s_%s", constants.RedisFirstKeyCustomerDataPoint, constants.KeyPiggybankGoalAmount, constants.ProductVariantPiggybank)
	// get redisKey
	redisData, err := getCustomerSearchDataPointFromRedis(ctx, redisKey, constants.CustomerSearchDataPointLogTag)
	if err != nil {
		return nil, err
	}
	if redisData != "" {
		var goalAmount string
		errMarshal := json.Unmarshal([]byte(redisData), &goalAmount)
		if errMarshal != nil {
			return nil, errorwrapper.WrapError(errMarshal, apiError.InternalServerError, "failed to unmarshal data")
		}
		return &api.CustomerSearchDataPointResponse{Data: redisData}, nil
	}

	request := &productMasterAPI.ListEffectiveProductVariantParametersRequest{
		ProductVariantCode: constants.ProductVariantPiggybank,
	}

	resp, err := helper.GetEffectiveProductVariant(ctx, request, p.ProductMasterClient, constants.CustomerSearchDataPointLogTag)
	if err != nil {
		return nil, err
	}

	parameters := resp.ProductVariantParameters
	if len(parameters) == 0 {
		return &api.CustomerSearchDataPointResponse{Data: ""}, nil
	}

	for _, param := range parameters {
		if param.ParameterKey == constants.PiggybankGoalAmountParameterKey {
			paramValue, errConv := strconv.ParseInt(param.ParameterValue, 10, 64)
			if errConv != nil {
				return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "error formatting data point")
			}
			humanizeVal := utils.HumanizeBalance(paramValue, false)
			// set data to redis
			errRedis := setCustomerSearchDataPointToRedis(ctx, redisKey, humanizeVal, constants.CustomerSearchDataPointLogTag, constants.MappingExpiryTimeForDataPoint[constants.KeyPiggybankGoalAmount])
			if errRedis != nil {
				return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "error set data point to redis")
			}
			return &api.CustomerSearchDataPointResponse{
				Data: humanizeVal,
			}, nil
		}
	}

	return &api.CustomerSearchDataPointResponse{Data: ""}, nil
}

// getPiggybankLastSuccessDeduction ...
func (p *process) getPiggybankLastSuccessDeduction(ctx context.Context, req *api.CustomerSearchDataPointRequest) (*api.CustomerSearchDataPointResponse, error) {
	accountID := req.Payload["accountID"]
	safeID := req.Payload["safeID"]
	if accountID == "" {
		return nil, errorwrapper.Error(apiError.BadRequest, "request has no valid accountID")
	}

	if safeID == "" {
		data, err := p.getGlobalDataByIdentifier(ctx, accountID, api.IdentifierType_ACCOUNT_NUMBER, constants.DataTypeSafeID)
		if err != nil {
			return nil, err
		}
		safeID = data
	}

	request := &txHistoryAPI.GetTransactionsHistoryRequest{
		AccountID: accountID,
		PageSize:  1,
	}

	resp, err := helper.GetTransactionList(ctx, safeID, request, p.TransactionHistoryClient, constants.CustomerSearchDataPointLogTag)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "error getting data point")
	}

	if len(resp.Data) == 0 {
		return &api.CustomerSearchDataPointResponse{Data: ""}, nil
	}
	data := resp.Data[0]
	return &api.CustomerSearchDataPointResponse{
		Data: data.UpdateTimestamp,
	}, nil
}

func (p *process) getGlobalDataByIdentifier(ctx context.Context, identifier string, identifierType api.IdentifierType, key string) (string, error) {
	resp, err := p.getCustomerData(ctx, identifier, string(identifierType), []string{constants.PersonalInfoType})
	if err != nil {
		return "", errorwrapper.Error(apiError.InternalServerError, "failed to get safeID for specific accountID")
	}
	var data string
	keyData := constants.MappingGlobalDataCustomerLeanKey[key]
	if id, exists := resp[keyData]; exists {
		data = id.(string)
		return data, nil
	}
	return "", errorwrapper.Error(apiError.BadRequest, fmt.Sprintf("invalid %s", identifierType))
}

// GetCustomerSearchDataPointFromRedis ...
func getCustomerSearchDataPointFromRedis(ctx context.Context, key string, logTag string) (string, error) {
	// get from redis
	data, err := redis.GetRedisValue(ctx, key, logTag)
	if err != nil {
		return "", errorwrapper.WrapError(err, apiError.InternalServerError, "Failed to get data point from redis")
	}

	return data, nil
}

// SetCustomerSearchDataPointToRedis ...
func setCustomerSearchDataPointToRedis(ctx context.Context, key string, value any, logTag string, expiry int64) error {
	err := redis.SetRedisValue(ctx, key, value, expiry, logTag)
	if err != nil {
		return err
	}
	return nil
}
