package logic

import (
	"context"
	"fmt"
	"strings"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"

	customerExperienceAPI "gitlab.myteksi.net/bersama/customer-experience/api"
	"gitlab.myteksi.net/dakota/servus/v2/slog"

	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"

	customerExperienceHttpAPI "gitlab.super-id.net/bersama/opsce/onedash-be/external/customerexperiencehttp"
)

// setVideoCallInformation will call when key is KeyVideoCallInformation
//
//nolint:funlen,gocognit // this function is logically complex and intentionally long
func (p process) setVideoCallInformation(ctx context.Context, data map[string]interface{}, customerID string, customerApplications *[]customerExperienceAPI.Applications) map[string]interface{} {
	var tickets []map[string]interface{}

	if customerApplications != nil {
		for _, app := range *customerApplications {
			resTickets, err := p.GetCustomerVideoCallTickets(ctx, app.ID, customerID)
			if err != nil {
				slog.FromContext(ctx).Error(constants.CustomerExperienceHTTPLogTag, constants.CustomerMasterLogErrorPrefix+err.Error(), utils.GetTraceID(ctx))
				return data
			}

			for _, resTicket := range *resTickets {
				var markerName, markerNote string
				var checkerName, checkerNote string

				// Extract Marker Info: first entry with Role == "Maker"
				for _, history := range resTicket.ApprovalHistory {
					if history.Role == "Maker" {
						markerName = history.User
						markerNote = history.Notes
						break
					}
				}

				// Extract Checker Info:
				// 1. Prefer role == "Fraud Ops"
				// 2. If not found, fall back to role == "Checker"
				for _, history := range resTicket.ApprovalHistory {
					if history.Role == "Fraud Ops" {
						checkerName = history.User
						checkerNote = history.Notes
						break
					}
				}
				if checkerName == "" { // No Fraud Ops found, look for Checker
					for _, history := range resTicket.ApprovalHistory {
						if history.Role == "Checker" {
							checkerName = history.User
							checkerNote = history.Notes
							break
						}
					}
				}

				var status string
				if len(resTicket.ApprovalHistory) > 0 {
					status = resTicket.ApprovalHistory[0].Status
				} else {
					status = "" // or some default/fallback value
				}

				markerInfo := ""
				if markerName != "" || markerNote != "" {
					markerInfo = fmt.Sprintf("%s, %s", markerName, markerNote)
				}

				checkerInfo := ""
				if checkerName != "" || checkerNote != "" {
					checkerInfo = fmt.Sprintf("%s, %s", checkerName, checkerNote)
				}

				ticket := map[string]interface{}{
					constants.KeyVideoCallInformationTicketID:  resTicket.TicketNumber,
					constants.KeyVideoCallInformationCreatedOn: utils.ConvertToWIBFormatted(resTicket.Details.VideoCallCreated),
					constants.KeyVideoCallInformationURL: &customerExperienceHttpAPI.VideoCallSupportingDocument{
						Type: "Click here to watch video",
						URL:  resTicket.Details.RecordingFile.URL,
					},
					constants.KeyVideoCallInformationAssessmentRemark: status,
					constants.KeyVideoCallInformationMarkerInfo:       markerInfo,
					constants.KeyVideoCallInformationCheckerInfo:      checkerInfo,
					constants.KeyVideoCallInformationDocument:         resTicket.SupportingDocuments,
				}

				tickets = append(tickets, ticket)
			}
		}
	}

	data[constants.KeyVideoCallInformation] = tickets

	return data
}

// setOnboardingHistorySection will call when key is KeyOnboardingHistorySection
//
//nolint:funlen // this function is logically complex and intentionally long
func setOnboardingHistorySection(data map[string]interface{}, customerApplications *[]customerExperienceAPI.Applications) map[string]interface{} {
	var applications []map[string]interface{}

	// Iterate through the applications of the first customer
	for _, app := range *customerApplications {
		application := map[string]interface{}{
			constants.KeyOnboardingHistorySectionID:          app.ID,
			constants.KeyOnboardingHistorySectionStatus:      app.Status,
			constants.KeyOnboardingHistorySectionSubmittedAt: utils.ConvertToWIBFormatted(app.SubmittedAt),
			constants.KeyOnboardingHistorySectionExpiredAt:   utils.ConvertToWIBFormatted(app.ExpiresAt),
			constants.KeyOnboardingHistorySectionEkyc:        app.Verifications.DukcapilDataMatchPartnerTrxID,
		}

		// If app.StatusRemarks is not empty or nil
		if app.StatusRemarks != "" {
			// Split the status remarks by ""
			parts := strings.Split(string(app.StatusRemarks), "")
			if len(parts) > 1 {
				// First part is RuleID
				ruleID := parts[0]
				// The rest is StatusRemarks
				statusRemark := strings.Join(parts[1:], "")

				application[constants.KeyOnboardingHistorySectionRuleID] = ruleID
				application[constants.KeyOnboardingHistorySectionStatusRemarks] = statusRemark
			} else {
				// If only one part exists, fallback handling
				application[constants.KeyOnboardingHistorySectionRuleID] = parts[0]
				application[constants.KeyOnboardingHistorySectionStatusRemarks] = ""
			}
		}

		// add detail
		var applicationDetails []map[string]interface{}
		for _, detail := range app.SelfieFile {
			applicationDetail := map[string]interface{}{
				constants.KeyOnboardingHistorySectionDetailType:       "Selfie",
				constants.KeyOnboardingHistorySectionDetailCreatedAt:  detail.CreatedAt,
				constants.KeyOnboardingHistorySectionDetailURL:        detail.URL,
				constants.KeyOnboardingHistorySectionDetailStatus:     detail.Status,
				constants.KeyOnboardingHistorySectionDetailReason:     detail.Reason,
				constants.KeyOnboardingHistorySectionDetailLivenessID: detail.TransactionID,
			}
			applicationDetails = append(applicationDetails, applicationDetail)
		}

		for _, detail := range app.KtpFile {
			applicationDetail := map[string]interface{}{
				constants.KeyOnboardingHistorySectionDetailType:      "KTP",
				constants.KeyOnboardingHistorySectionDetailCreatedAt: detail.CreatedAt,
				constants.KeyOnboardingHistorySectionDetailURL:       detail.URL,
				constants.KeyOnboardingHistorySectionDetailStatus:    detail.Status,
				constants.KeyOnboardingHistorySectionDetailReason:    detail.Reason,
			}
			applicationDetails = append(applicationDetails, applicationDetail)
		}

		application[constants.KeyOnboardingHistorySectionDetails] = applicationDetails

		applications = append(applications, application)
	}

	data[constants.KeyOnboardingHistorySection] = applications

	return data
}

func setOCRDetails(data map[string]interface{}, ocrData *customerExperienceAPI.CustomerRelatedData) map[string]interface{} {
	var customerData []map[string]interface{}

	submittedByUser := map[string]interface{}{
		constants.KeyOCRTransactionID: ocrData.OCRResponseData.TransactionID,
		constants.KeyOCRSource:        ocrData.OCRResponseData.Source,
		constants.KeyOCRFullName:      ocrData.FullName,
		constants.KeyOCRNIK:           ocrData.NIK,
		constants.KeyOCRDateOfBirth:   ocrData.DateOfBirth,
		constants.KeyOCRMaritalStatus: ocrData.MaritalStatus,
		constants.KeyOCRPlaceOfBirth:  ocrData.PlaceOfBirth,
		constants.KeyOCRGender:        ocrData.Gender,
		constants.KeyOCROccupation:    "",
		constants.KeyOCRNationality:   ocrData.Nationality,
		constants.KeyOCRStreet:        "",
		constants.KeyOCRCity:          "",
		constants.KeyOCRProvince:      "",
		constants.KeyOCRSubdistrict:   "",
		constants.KeyOCRVillage:       "",
		constants.KeyOCRRT:            "",
		constants.KeyOCRRW:            "",
	}

	ocrResult := map[string]interface{}{
		constants.KeyOCRTransactionID: ocrData.OCRResponseData.TransactionID,
		constants.KeyOCRSource:        ocrData.OCRResponseData.Source,
		constants.KeyOCRFullName:      ocrData.OCRResponseData.FullName,
		constants.KeyOCRNIK:           ocrData.OCRResponseData.NIK,
		constants.KeyOCRDateOfBirth:   ocrData.OCRResponseData.DateOfBirth,
		constants.KeyOCRMaritalStatus: ocrData.OCRResponseData.MaritalStatus,
		constants.KeyOCRPlaceOfBirth:  ocrData.OCRResponseData.PlaceOfBirth,
		constants.KeyOCRGender:        ocrData.OCRResponseData.Gender,
		constants.KeyOCROccupation:    ocrData.OCRResponseData.Occupation,
		constants.KeyOCRNationality:   ocrData.OCRResponseData.Nationality,
		constants.KeyOCRStreet:        ocrData.OCRResponseData.Street,
		constants.KeyOCRCity:          ocrData.OCRResponseData.City,
		constants.KeyOCRProvince:      ocrData.OCRResponseData.Province,
		constants.KeyOCRSubdistrict:   ocrData.OCRResponseData.Subdistrict,
		constants.KeyOCRVillage:       ocrData.OCRResponseData.Village,
		constants.KeyOCRRT:            ocrData.OCRResponseData.RT,
		constants.KeyOCRRW:            ocrData.OCRResponseData.RW,
	}

	customerData = append(customerData, submittedByUser, ocrResult)

	data[constants.KeyOCRDetails] = customerData

	return data
}

func (p process) mappingOnboardingHistory(ctx context.Context, ID string, identifierType string, key string) (map[string]interface{}, error) {
	data := make(map[string]interface{})

	// Fetch customer data
	resp, err := p.GetCustomerByIdentifier(ctx, ID, identifierType, 1)
	if err != nil {
		return nil, errorwrapper.WrapError(err, apiError.InternalServerError, "error getting customer related data by identifier")
	}

	// Null and bounds check
	if resp == nil || len(resp.Items) == 0 {
		return data, nil
	}

	item := resp.Items[0]

	// Defensive check: ensure Customer is valid
	if item.Customer.ID == "" {
		// If ID is missing, assume customer is not valid (customize if needed)
		return data, nil
	}
	customerInfo := item.Customer

	// Defensive check: applications must be non-nil and non-empty when needed
	customerApplications := item.Applications
	switch key {
	case constants.KeyOCRDetails:
		setOCRDetails(data, &customerInfo)

	case constants.KeyOnboardingHistorySection:
		if customerApplications == nil || len(customerApplications) == 0 {
			return data, nil
		}
		setOnboardingHistorySection(data, &customerApplications)

	case constants.KeyVideoCallInformation:
		if customerApplications == nil {
			return data, nil
		}
		p.setVideoCallInformation(ctx, data, customerInfo.ID, &customerApplications)
	}

	return data, nil
}
