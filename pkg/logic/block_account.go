package logic

import (
	"context"
	"time"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	accountServiceAPI "gitlab.myteksi.net/bersama/core-banking/account-service/api"
	commonCtx "gitlab.myteksi.net/dakota/common/context"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils/validations"
)

// BlockAccountRequest ...
type BlockAccountRequest struct {
	AccountID          string   `json:"AccountID" validate:"account_id"`
	IdempotencyKey     string   `json:"IdempotencyKey" validate:"required"`
	HoldCodes          []string `json:"HoldCodes" validate:"block_unblock_hold_codes"`
	UpdatedBy          string   `json:"UpdatedBy"`
	TicketID           string   `json:"TicketID"`
	SafeID             string   `json:"SafeID"` // Can be removed from the request by get by account id
	IsSendNotification bool     `json:"IsSendNotification"`
}

// BlockAccountResponse ...
type BlockAccountResponse struct {
	AccountID     string `json:"accountID,omitempty"`
	Status        string `json:"status,omitempty"`
	FailureReason string `json:"failureReason,omitempty"`
	QueueFeedback
}

// BlockAccountNotificationParams ...
type BlockAccountNotificationParams struct {
	UserName       string `json:"user_name"`
	AccountWording string `json:"account_wording"`
	AccountName    string `json:"account_name"`
	AccountIDs     string `json:"account_ids"`
	MessageDate    string `json:"message_date"`
}

// BlockAccount is the business logic for blocking an account.
//
// nolint: gocyclo, funlen, dupl
func (p *process) BlockAccount(ctx context.Context, req *BlockAccountRequest) (*BlockAccountResponse, error) {
	if req == nil {
		return nil, errorwrapper.Error(apiError.BadRequest, "request is nil")
	}
	ok, err := validations.IsValid(req)
	if !ok {
		if err != nil {
			return nil, errorwrapper.Error(apiError.BadRequest, err.Error())
		}
		return nil, errorwrapper.Error(apiError.BadRequest, "request is invalid")
	}

	var res = &BlockAccountResponse{}

	// fetch the applicable hold codes
	currentHoldCodes, err := p.getCurrentAccountHoldCodes(ctx, req.AccountID)
	if err != nil {
		slog.FromContext(ctx).Error(blockAccountLogTag, "error fetching hold codes", slog.Error(err))
		return nil, errorwrapper.WrapError(err, apiError.Idem, "error fetching hold codes")
	}

	// add business validation, if account is already blocked then return error
	updatedHoldCodes, isAlreadyExist := addBlockAccountHoldCode(currentHoldCodes, req.HoldCodes)
	if isAlreadyExist {
		return nil, errorwrapper.Error(apiError.BadRequest, "account is already blocked")
	}

	// send request to block account
	ctx = commonCtx.WithHTTPHeader(ctx, commonCtx.HeaderIdempotencyKey, req.IdempotencyKey)
	resUpdate, err := p.AccountServiceClient.UpdateCASAAccountParameters(ctx, &accountServiceAPI.UpdateCASAAccountParametersRequest{
		AccountID: req.AccountID,
		ProductSpecificParameters: &accountServiceAPI.CASAAccountParams{
			ApplicableHoldcodes: updatedHoldCodes,
		},
		UpdatedBy: req.UpdatedBy,
	})
	if err != nil {
		return &BlockAccountResponse{QueueFeedback: QueueFeedback{NeedRequeue: true}}, errorwrapper.WrapError(err, apiError.Idem, "error updating account")
	}

	res = &BlockAccountResponse{
		AccountID:     resUpdate.AccountID,
		Status:        resUpdate.Status,
		FailureReason: resUpdate.FailureReason,
	}

	// send notification if required
	if req.IsSendNotification {
		params, err := structToMap(BlockAccountNotificationParams{
			UserName:       "", // FIXME
			AccountWording: "", // FIXME
			AccountName:    "", // FIXME
			AccountIDs:     "", // FIXME
			MessageDate:    time.Now().Format("2006-01-02 15:04:05"),
		})
		if err != nil {
			return nil, errorwrapper.WrapError(err, apiError.Idem, "error marshalling notification params")
		}

		// FIXME: send notification email
		p.SendNotification(ctx, SendNotificationAction{
			ActionType:       constants.BlockAccountAction,
			SafeID:           req.AccountID,
			TicketID:         req.TicketID,
			NotificationType: api.SendNotificationType_EMAIL,
			Params:           params,
		})

		// FIXME: send notification push inbox, fix params
		p.SendNotification(ctx, SendNotificationAction{
			ActionType:       constants.BlockAccountAction,
			SafeID:           req.AccountID,
			TicketID:         req.TicketID,
			NotificationType: api.SendNotificationType_PUSH_INBOX,
			Params:           params,
		})
	}

	return &BlockAccountResponse{
		AccountID:     res.AccountID,
		Status:        res.Status,
		FailureReason: res.FailureReason,
	}, nil
}

func addBlockAccountHoldCode(holdCodes []accountServiceAPI.ApplicableHoldcode, req []string) ([]accountServiceAPI.ApplicableHoldcode, bool) {
	var existing = make(map[accountServiceAPI.ApplicableHoldcode]bool)
	var final = make([]accountServiceAPI.ApplicableHoldcode, 0)
	// check if the hold code is already present & make map for faster lookup
	for _, code := range holdCodes {
		if constants.AllowedHoldCodes[code] {
			return holdCodes, true
		}
		existing[code] = true
	}
	final = append(final, holdCodes...)

	// add the hold codes from request
	for _, code := range req {
		// if account already have the hold code then will reject the request
		if existing[accountServiceAPI.ApplicableHoldcode(code)] {
			return holdCodes, true
		}
		final = append(final, accountServiceAPI.ApplicableHoldcode(code))
	}

	return final, false
}
