package helper

import (
	"context"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	productMaster "gitlab.myteksi.net/bersama/core-banking/product-master/api"
	commonCtx "gitlab.myteksi.net/dakota/common/context"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"
)

// LoanInstruction ...
type LoanInstruction struct {
	Code string `json:"code"`
	Name string `json:"name"`
}

func logErrorProductMaster(ctx context.Context, err error, logTag string) {
	slog.FromContext(ctx).Warn(logTag, constants.ProductMasterLogErrorPrefix+err.Error(), utils.GetTraceID(ctx))
}

// GetEffectiveProductVariant ...
func GetEffectiveProductVariant(ctx context.Context, request *productMaster.ListEffectiveProductVariantParametersRequest, p productMaster.ProductMaster, logTag string) (*productMaster.ListEffectiveProductVariantParametersResponse, error) {
	response, err := p.ListEffectiveProductVariantParameters(ctx, request)
	if err != nil {
		logErrorProductMaster(ctx, err, logTag)
		return nil, err
	}
	return response, nil
}

// GetLoanInstructions ...
func GetLoanInstructions(ctx context.Context, productVariantCode, instructionType string, p productMaster.ProductMaster, logTag string) ([]LoanInstruction, error) {
	if productVariantCode == "" {
		productVariantCode = constants.ProductVariantLineOfCredit
	}

	if instructionType == "" {
		instructionType = constants.InstructionTypeBlockUnblock
	}

	getLoanInsReq := &productMaster.GetLoanInstructionsByCodeRequest{
		ProductVariantCode: productVariantCode,
		InstructionType:    instructionType,
		Version:            "1",
	}

	withHTTPHeader := commonCtx.WithHTTPHeader(ctx, commonCtx.HeaderXServiceID, constants.DIGIBANK)

	response, err := p.GetLoanInstructionsByCode(withHTTPHeader, getLoanInsReq)
	if err != nil {
		logErrorProductMaster(ctx, err, logTag)

		if serviceErr, ok := err.(servus.ServiceError); ok {
			if serviceErr.HTTPCode == 404 {
				return nil, nil
			}
		}
		return nil, errorwrapper.Error(apiError.InternalServerError, err.Error())
	}

	return processLoanInstructions(response), nil
}

func processLoanInstructions(response *productMaster.GetLoanInstructionsByCodeResponse) []LoanInstruction {
	if response == nil {
		return []LoanInstruction{}
	}

	var loanInstructions []LoanInstruction
	instructions := response.LoanInstruction

	for _, instruction := range instructions {
		loanInstructions = append(loanInstructions, LoanInstruction{
			Code: instruction.Code,
			Name: instruction.Name,
		})
	}

	return loanInstructions
}
