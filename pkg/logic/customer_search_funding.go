package logic

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"sync"
	"time"

	apiError "gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/error"
	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/common/utils/errorwrapper"
	PayAuthZApi "gitlab.myteksi.net/dakota/payment/pay-authz/api"
	customerJournalAPI "gitlab.super-id.net/bersama/corex/customer-journal/api"
	permissionManagementStorage "gitlab.super-id.net/bersama/opsce/onedash-be/module/permissionmanagement/storage"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils/slogwrapper"

	"strconv"

	accountServiceAPI "gitlab.myteksi.net/bersama/core-banking/account-service/api"
	productMasterAPI "gitlab.myteksi.net/bersama/core-banking/product-master/api"
	txHistoryAPI "gitlab.myteksi.net/bersama/transaction-history/api"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	txnLimitAPI "gitlab.myteksi.net/dakota/transaction-limit/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/api"
	"gitlab.super-id.net/bersama/opsce/onedash-be/constants"
	"gitlab.super-id.net/bersama/opsce/onedash-be/pkg/logic/helper"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"
)

func (p process) mappingFundingRelatedData(ctx context.Context, req *api.CustomerSearchRequest, user *permissionManagementStorage.UserDTO) (map[string]interface{}, error) {
	result := make(map[string]interface{})
	var err error

	switch req.Key {
	case constants.KeyMainCasaAndSakuAccount, constants.KeyPiggybank, constants.KeyTermDeposit:
		result, err = p.GetFundingAccountList(ctx, req)
	case constants.KeyTermDepositRenewalHistory:
		result, err = p.GetTermDepositRenewalHistory(ctx, req)
	case constants.KeyTermDepositParameterHistory:
		result, err = p.GetDepositParameterHistory(ctx, req)
	// transaction configuration data
	case constants.KeyCustomerLevelTxLimit, constants.KeyBankWideLevelTxLimit:
		result, err = p.GetTransactionConfigurationData(ctx, req, user)
	case constants.KeyPartnerLinkage:
		result, err = p.GetExternalLinkageData(ctx, req)
	case constants.KeyLinkingHistory:
		result, err = p.GetLinkingHistory(ctx, req)
	case constants.KeyReauthHistory:
		result, err = p.GetReauthenticationHistory(ctx, req)
	}
	if err != nil {
		return nil, err
	}

	if req.IdentifierType == api.IdentifierType_SAFE_ID {
		result[constants.DataTypeSafeID] = req.Identifier
	} else if req.IdentifierType == api.IdentifierType_CIF {
		result[constants.DataTypeCif] = req.Identifier
	}

	return result, nil
}

// GetAccountListCustomerSearch ...
func (p process) GetAccountListCustomerSearch(ctx context.Context, req *api.CustomerSearchRequest) (map[string]interface{}, error) {
	var cifNumber string

	if req.IdentifierType == api.IdentifierType_CIF {
		cifNumber = req.Identifier
	} else if req.Payload != nil && req.Payload[constants.DataTypeCif] != "" {
		cifNumber = req.Payload[constants.DataTypeCif]
	} else {
		resp, err := p.GetCustomerSafeID(ctx, req.Identifier, string(req.IdentifierType), req.Key)
		if err != nil {
			return nil, err
		}
		cifNumber = resp[constants.DataTypeCif].(string)
	}

	if cifNumber == "" {
		return nil, errorwrapper.Error(apiError.BadRequest, "user has no valid cif number")
	}

	request := &accountServiceAPI.ListCASAAccountsForCustomerDetailRequest{
		CifNumber: cifNumber,
	}

	resp, err := helper.GetAccountList(ctx, request, p.AccountServiceClient, constants.CustomerSearchLogTag)
	if err != nil {
		return nil, err
	}

	result := mappingAccountListField(resp, req.Key)
	// include cif in response
	result[constants.DataTypeCif] = cifNumber
	return result, nil
}

// GetFundingAccountList ...
func (p process) GetFundingAccountList(ctx context.Context, req *api.CustomerSearchRequest) (map[string]interface{}, error) {
	var cifNumber string
	var safeID string
	accountName := ""

	if req.IdentifierType == api.IdentifierType_CIF {
		cifNumber = req.Identifier
	} else if req.Payload != nil && req.Payload[constants.DataTypeCif] != "" {
		cifNumber = req.Payload[constants.DataTypeCif]
	} else {
		resp, err := p.getCustomerData(ctx, req.Identifier, string(req.IdentifierType), []string{constants.PersonalInfoType})
		if err != nil {
			return nil, err
		}
		cifNumber = resp[constants.MappingGlobalDataCustomerLeanKey[constants.DataTypeCif]].(string)
		accountName = resp["name"].(string)
		safeID = resp[constants.MappingGlobalDataCustomerLeanKey[constants.DataTypeSafeID]].(string)
	}

	if cifNumber == "" {
		return nil, errorwrapper.Error(apiError.BadRequest, "user has no valid cif number")
	}

	productVariantIDs := constants.ProductVariantMappingBySubTab[req.Key]

	request := &accountServiceAPI.ListCASAAccountsForCustomerDetailRequest{
		CifNumber:         cifNumber,
		ProductVariantIDs: productVariantIDs,
		FetchInterestRate: true,
		FetchBalance:      true,
	}

	resp, err := helper.GetAccountList(ctx, request, p.AccountServiceClient, constants.CustomerSearchLogTag)
	if err != nil {
		return nil, err
	}

	data := p.mappingFundingAccountDetail(ctx, resp, req.Key, accountName, safeID)
	// include cif in response
	result := map[string]interface{}{
		req.Key: data,
	}
	result[constants.DataTypeCif] = cifNumber
	return result, nil
}

// mappingAccountListField ...
func mappingAccountListField(data *accountServiceAPI.ListCASAAccountsForCustomerDetailResponse, key string) map[string]interface{} {
	result := make(map[string]interface{})

	if data == nil || len(data.Accounts) == 0 {
		return result
	}

	switch key {
	case constants.KeyLendingPAS:
		setListOfLendingAccounts(result, data)
	case constants.KeyTransactionList:
		setListOfTxAccountFilter(result, data)
	}
	return result
}

// setListOfLendingAccounts ...
func setListOfLendingAccounts(data map[string]interface{}, accData *accountServiceAPI.ListCASAAccountsForCustomerDetailResponse) {
	accountMap := filterByVariantForLending(accData.Accounts)
	lendingInfo := map[string]interface{}{
		constants.KeyLocInformation: getAccountBySubTab(accountMap, constants.KeyLendingPAS),
	}

	for k, v := range lendingInfo {
		data[k] = v
	}
}

// setListOfTxAccountFilter ...
func setListOfTxAccountFilter(data map[string]any, accData *accountServiceAPI.ListCASAAccountsForCustomerDetailResponse) {
	accountMap := filterByVariantForFunding(accData.Accounts, true)
	fundingInfo := map[string]any{
		constants.KeyMainCasa:    getAccountBySubTab(accountMap, constants.KeyMainCasa),
		constants.KeySaku:        getAccountBySubTab(accountMap, constants.KeySaku),
		constants.KeyOVONabung:   getAccountBySubTab(accountMap, constants.KeyOVONabung),
		constants.KeyPiggybank:   getAccountBySubTab(accountMap, constants.KeyPiggybank),
		constants.KeyTermDeposit: getAccountBySubTab(accountMap, constants.KeyTermDeposit),
	}

	for k, v := range fundingInfo {
		data[k] = v
	}
}

func filterByVariantForLending(accounts []accountServiceAPI.CASAAccountDetail) map[string][]interface{} {
	filteredAccounts := make(map[string][]interface{})
	for _, acc := range accounts {
		curAcc := map[string]interface{}{
			"accountID":        acc.Id,
			"productVariantID": acc.ProductVariantID,
			"parentAccountID":  acc.ParentAccountID,
			"createdAt":        acc.OpeningTimestamp,
			"status":           acc.Status,
		}
		if listAcc, ok := filteredAccounts[acc.ProductVariantID]; ok {
			filteredAccounts[acc.ProductVariantID] = append(listAcc, curAcc)
		} else {
			filteredAccounts[acc.ProductVariantID] = []interface{}{curAcc}
		}
	}
	return filteredAccounts
}

func getAccountBySubTab(data map[string][]interface{}, subTab string) []interface{} {
	accounts := make([]interface{}, 0)
	productVariantIDs := constants.ProductVariantMappingBySubTab[subTab]
	if len(productVariantIDs) == 0 {
		return accounts
	}

	for _, varID := range productVariantIDs {
		if listAcc, ok := data[varID]; ok {
			accounts = append(accounts, listAcc...)
		}
	}
	return accounts
}

func filterByVariantForFunding(accounts []accountServiceAPI.CASAAccountDetail, ignorePendingAccount bool) map[string][]interface{} {
	filteredAccounts := make(map[string][]interface{})
	for _, acc := range accounts {
		if ignorePendingAccount && acc.Id == "" {
			continue
		}
		curAcc := map[string]interface{}{
			"accountID":        acc.Id,
			"productVariantID": acc.ProductVariantID,
			"status":           acc.Status,
			"accountName":      acc.AccountName,
		}
		if listAcc, ok := filteredAccounts[acc.ProductVariantID]; ok {
			filteredAccounts[acc.ProductVariantID] = append(listAcc, curAcc)
		} else {
			filteredAccounts[acc.ProductVariantID] = []interface{}{curAcc}
		}
	}
	return filteredAccounts
}

func (p *process) mappingFundingAccountDetail(ctx context.Context, data *accountServiceAPI.ListCASAAccountsForCustomerDetailResponse, key string, mainAccName string, safeID string) map[string]interface{} {
	accounts := make([]interface{}, 0)
	accountsData := make([]map[string]interface{}, 0)

	if data == nil || len(data.Accounts) == 0 {
		return make(map[string]interface{})
	}

	for _, acc := range data.Accounts {
		// accounts for sub tab label purposes
		curAcc := map[string]interface{}{
			"accountID":        acc.Id,
			"productVariantID": acc.ProductVariantID,
			"status":           acc.Status,
		}
		accounts = append(accounts, curAcc)

		currentData := make(map[string]interface{})
		switch key {
		case constants.KeyMainCasaAndSakuAccount:
			p.setMainCasaAndPocketAccount(ctx, currentData, acc, mainAccName)
		case constants.KeyPiggybank:
			p.setPiggybankAccount(ctx, currentData, acc, safeID)
		case constants.KeyTermDeposit:
			setTermDepositAccount(currentData, acc)
		}
		accountsData = append(accountsData, currentData)
	}

	result := map[string]interface{}{
		"accounts": accounts,
		"data":     accountsData,
	}
	return result
}

func (p *process) setMainCasaAndPocketAccount(ctx context.Context, result map[string]interface{}, data accountServiceAPI.CASAAccountDetail, mainCasaName string) {
	accountName := data.AccountName

	var blockHoldcodes []string
	err := json.Unmarshal([]byte(data.ProductSpecificParameters["applicableHoldcodes"]), &blockHoldcodes)
	if err != nil {
		slog.FromContext(ctx).Error(constants.CustomerSearchLogTag, "Unable to unmarshal account holdcodes", utils.GetTraceID(ctx))
	}

	// get account name when CASA
	if data.ProductVariantID == constants.ProductVariantMainCasa {
		if mainCasaName == "" {
			custRes, custErr := p.getCustomerData(ctx, data.Id, string(api.IdentifierType_ACCOUNT_NUMBER), []string{constants.PersonalInfoType})
			if custErr != nil {
				slogwrapper.FromContext(ctx).Error(constants.CustomerSearchLogTag, "error to get piggybank goal amount : "+custErr.Error(), utils.GetTraceID(ctx))
			}
			accountName = custRes["name"].(string)
		} else {
			accountName = mainCasaName
		}
	}

	info := map[string]interface{}{
		constants.KeyMainCasaAccountID:              data.Id,
		constants.KeyMainCasaAccountType:            constants.MappingWordingAccountType[data.ProductVariantID],
		constants.KeyMainCasaAccountStatus:          data.Status,
		constants.KeyMainCasaAccountName:            accountName,
		constants.KeyMainCasaBlockHoldcodes:         blockHoldcodes,
		constants.KeyMainCasaMakerName:              "",
		constants.KeyMainCasaMakerJustification:     "",
		constants.KeyMainCasaLastUpdated:            "", //the data not ready
		constants.KeyMainCasaCurrency:               data.AvailableBalance.CurrencyCode,
		constants.KeyMainCasaAvailableBalance:       utils.HumanizeBalance(data.AvailableBalance.Val, true),
		constants.KeyMainCasaBaseInterestRate:       utils.FormatWithPercentage(utils.HumanizeBalance(data.ApplicableInterestRate.BaseInterestRate, false)),
		constants.KeyMainCasaTotalInterestRate:      utils.FormatWithPercentage(utils.HumanizeBalance(data.ApplicableInterestRate.TotalInterestRate, false)),
		constants.KeyMainCasaRateSpreadInterestRate: utils.FormatWithPercentage(utils.HumanizeBalance(data.ApplicableInterestRate.RateSpreadInterestRate, false)),
	}

	for k, v := range info {
		result[k] = v
	}
}

func (p *process) setPiggybankAccount(ctx context.Context, result map[string]interface{}, data accountServiceAPI.CASAAccountDetail, safeID string) {
	var (
		goalAmount string
		gaErr      error
		lastDeduct string
		ldErr      error
		wg         sync.WaitGroup
	)
	wg.Add(2)

	go func() {
		defer wg.Done()
		goalAmount, gaErr = p.getCustomerSearchPiggyGoalAmount(ctx)
	}()

	go func() {
		defer wg.Done()
		lastDeduct, ldErr = p.getCustomerSearchPiggyLastDeduct(ctx, data.Id, safeID)
	}()

	wg.Wait()

	if gaErr != nil {
		slogwrapper.FromContext(ctx).Error(constants.CustomerSearchLogTag, "error to get piggybank goal amount : "+gaErr.Error(), utils.GetTraceID(ctx))
	}

	if ldErr != nil {
		slogwrapper.FromContext(ctx).Error(constants.CustomerSearchLogTag, "error to get piggybank last deduct : "+ldErr.Error(), utils.GetTraceID(ctx))
	}

	piggyInfo := map[string]interface{}{
		constants.KeyPiggybankAccountID:                   data.Id,
		constants.KeyPiggybankInterestRate:                utils.FormatInterestValue(utils.HumanizeBalance(data.ApplicableInterestRate.TotalInterestRate, false)),
		constants.KeyPiggybankBalanceWithInterest:         utils.HumanizeBalance(data.AvailableBalance.Val, true),
		constants.KeyPiggybankOpeningTime:                 data.OpeningTimestamp,
		constants.KeyPiggybankBreakDate:                   data.ClosingTimestamp,
		constants.KeyPiggybankNearestDeductionAmount:      utils.HumanizeBalance(data.ProductSpecificParameters["nominalAmount"], false),
		constants.KeyPiggybankStatus:                      data.Status,
		constants.KeyPiggybankSourceOfFund:                data.ProductSpecificParameters["microsaverFundsSourceAccount"],
		constants.KeyPiggybankLevel:                       data.ProductSpecificParameters["tierName"],
		constants.KeyPiggybankBalance:                     "",
		constants.KeyPiggybankNearestDeductionSettingDate: "",
		constants.KeyPiggybankAutoIsi:                     constants.MappingPiggybankBooster[data.ProductSpecificParameters["isBoosterFlagEnabled"]],
		constants.KeyPiggybankAutoIsiSettingDate:          "",
		constants.KeyPiggybankClosureType:                 "",
		constants.KeyPiggybankGoalAmount:                  goalAmount,
		constants.KeyPiggybankLastSuccessDeduction:        lastDeduct,
	}

	for k, v := range piggyInfo {
		result[k] = v
	}
}

func (p *process) getCustomerSearchPiggyGoalAmount(ctx context.Context) (string, error) {
	redisKey := fmt.Sprintf("%s_%s_%s", constants.RedisFirstKeyCustomerDataPoint, constants.KeyPiggybankGoalAmount, constants.ProductVariantPiggybank)
	// get redisKey
	redisData, hasData, err := getStringDataPointFromRedis(ctx, redisKey)
	if hasData {
		var goalAmount string
		errMarshal := json.Unmarshal([]byte(redisData), &goalAmount)
		if errMarshal != nil {
			return "", errorwrapper.WrapError(errMarshal, apiError.InternalServerError, "failed to unmarshal data")
		}
		return goalAmount, nil
	}
	if err != nil {
		return "", nil
	}

	request := &productMasterAPI.ListEffectiveProductVariantParametersRequest{
		ProductVariantCode: constants.ProductVariantPiggybank,
	}

	resp, err := helper.GetEffectiveProductVariant(ctx, request, p.ProductMasterClient, constants.CustomerSearchDataPointLogTag)
	if err != nil {
		return "", err
	}

	parameters := resp.ProductVariantParameters
	if len(parameters) == 0 {
		return "", nil
	}

	for _, param := range parameters {
		if param.ParameterKey == constants.PiggybankGoalAmountParameterKey {
			paramValue, errConv := strconv.ParseInt(param.ParameterValue, 10, 64)
			if errConv != nil {
				return "", errorwrapper.WrapError(err, apiError.InternalServerError, "error formatting data point")
			}
			humanizeVal := utils.HumanizeBalance(paramValue, false)
			// set data to redis
			errRedis := setCustomerSearchDataPointToRedis(ctx, redisKey, humanizeVal, constants.CustomerSearchDataPointLogTag, constants.MappingExpiryTimeForDataPoint[constants.KeyPiggybankGoalAmount])
			if errRedis != nil {
				return "", errorwrapper.WrapError(err, apiError.InternalServerError, "error set data point to redis")
			}
			return humanizeVal, nil
		}
	}

	return "", nil
}

func getStringDataPointFromRedis(ctx context.Context, redisKey string) (string, bool, error) {
	redisData, err := getCustomerSearchDataPointFromRedis(ctx, redisKey, constants.CustomerSearchDataPointLogTag)
	if err != nil {
		return "", false, err
	}

	if redisData != "" {
		var data string
		errMarshal := json.Unmarshal([]byte(redisData), &data)
		if errMarshal != nil {
			return "", false, errorwrapper.WrapError(errMarshal, apiError.InternalServerError, "failed to unmarshal data")
		}
		return redisData, true, nil
	}

	return "", false, nil
}

// getLastSuccessDeduction ...
func (p *process) getCustomerSearchPiggyLastDeduct(ctx context.Context, accountID string, safeID string) (string, error) {
	if accountID == "" {
		return "", nil
	}

	if safeID == "" {
		resp, err := p.GetCustomerSafeID(ctx, accountID, string(api.IdentifierType_ACCOUNT_NUMBER), constants.KeyPiggybankLastSuccessDeduction)
		if err != nil {
			return "", err
		}
		safeID = resp[constants.DataTypeSafeID].(string)
	}

	request := &txHistoryAPI.GetTransactionsHistoryRequest{
		AccountID: accountID,
		PageSize:  1,
	}

	resp, err := helper.GetTransactionList(ctx, safeID, request, p.TransactionHistoryClient, constants.CustomerSearchLogTag)
	if err != nil {
		return "", errorwrapper.WrapError(err, apiError.InternalServerError, "error getting data point")
	}

	if len(resp.Data) == 0 {
		return "", nil
	}
	data := resp.Data[0].UpdateTimestamp.String()
	return data, nil
}

func setTermDepositAccount(result map[string]interface{}, data accountServiceAPI.CASAAccountDetail) {
	var tdPrincipalAmount, tdEarnedInterest, tdBalanceWithInterest float64
	aro := data.ProductSpecificParameters["tdMaturityInstructionType"]
	showRenewal := true
	tdPrincipalAmount, _ = strconv.ParseFloat(data.ProductSpecificParameters["tdPrincipalAmount"], 64)
	tdEarnedInterest, _ = strconv.ParseFloat(data.ProductSpecificParameters["expectedTDInterest"], 64)
	tdBalanceWithInterest = tdPrincipalAmount + tdEarnedInterest

	if aro == "" || aro == constants.NonAROType {
		showRenewal = false
	}

	tdInfo := map[string]interface{}{
		constants.KeyTermDepositAccountNumber:               data.Id,
		constants.KeyTermDepositPrincipalAmount:             utils.HumanizeBalance(tdPrincipalAmount, false),
		constants.KeyTermDepositBalanceWithEstimateInterest: utils.HumanizeBalance(tdBalanceWithInterest, false),
		constants.KeyTermDepositEstimatedEarnedInterest:     utils.HumanizeBalance(tdEarnedInterest, false),
		constants.KeyTermDepositEstimatedTaxDeduction:       "",
		constants.KeyTermDepositSourceOfFund:                data.ProductSpecificParameters["tdSourceOfFund"],
		constants.KeyTermDepositStatus:                      data.Status,
		constants.KeyTermDepositInterestRate:                utils.FormatInterestValue(utils.HumanizeBalance(data.ProductSpecificParameters["applicableInterestRate"], false)),
		constants.KeyTermDepositInterestRateType:            "",
		constants.KeyTermDepositAroMechanism:                getAroMechanismMap(aro),
		constants.KeyTermDepositEarliestManualBreakDate:     "",
		constants.KeyTermDepositClosureType:                 data.ProductSpecificParameters["tdCloseType"],
		constants.KeyTermDepositIssuanceDate:                utils.DateAsString(data.OpeningTimestamp),
		constants.KeyTermDepositMaturityDate:                data.ProductSpecificParameters["tdMaturityDate"],
		constants.KeyTermDepositPlacementTenure:             data.ProductSpecificParameters["tdTenor"],
		constants.KeyTermDepositPlacementTenureType:         data.ProductSpecificParameters["tdTenorType"],
		constants.KeyTermDepositBreakTimestamp:              data.ClosingTimestamp,
		constants.KeyTermDepositParameterHistory:            true,
		constants.KeyTermDepositRenewalHistory:              showRenewal,
	}

	for k, v := range tdInfo {
		result[k] = v
	}
}

// GetDepositParameterHistory ...
func (p process) GetDepositParameterHistory(ctx context.Context, req *api.CustomerSearchRequest) (map[string]interface{}, error) {
	accountID := req.Payload["accountID"]

	if accountID == "" {
		return nil, errorwrapper.Error(apiError.BadRequest, "need account id as payload to request")
	}
	request := &accountServiceAPI.GetDepositsParameterHistoryRequest{
		AccountID:    accountID,
		ParameterKey: constants.TermDepositParameterKeyMaturityInstruction,
	}
	resp, err := helper.GetDepositParameterHistory(ctx, request, p.AccountServiceClient, constants.CustomerSearchLogTag)
	if err != nil {
		return nil, err
	}
	return setTDDepositParameterHistory(resp), nil
}

func setTDDepositParameterHistory(data *accountServiceAPI.GetDepositsParameterHistoryResponse) map[string]interface{} {
	result := make(map[string]interface{})
	if len(data.DepositsParameterHistory) == 0 {
		return result
	}

	var finalData []map[string]interface{}
	for i := len(data.DepositsParameterHistory) - 1; i >= 0; i-- {
		val := data.DepositsParameterHistory[i]
		fmt.Println(val.NewParameterValue)
		curData := map[string]interface{}{
			constants.KeyDepositParamTimestamp:      utils.ParsingAndFormatTime(val.UpdatedAt, time.DateTime, constants.OnedashTimeLayout, true),
			constants.KeyDepositParamMaturityBefore: val.OldParameterValue,
			constants.KeyDepositParamMaturityAfter:  val.NewParameterValue,
			constants.KeyDepositParamUpdatedBy:      val.UpdatedBy,
		}
		finalData = append(finalData, curData)
	}
	result[constants.KeyTermDepositParameterHistory] = finalData
	return result
}

// GetTermDepositRenewalHistory ...
func (p process) GetTermDepositRenewalHistory(ctx context.Context, req *api.CustomerSearchRequest) (map[string]interface{}, error) {
	accountID := req.Payload["accountID"]

	if accountID == "" {
		return nil, errorwrapper.Error(apiError.BadRequest, "need account id as payload to request")
	}
	request := &accountServiceAPI.DepositsParameterRenewalHistoryRequest{
		AccountID: accountID,
	}
	resp, err := helper.GetRenewalHistory(ctx, request, p.AccountServiceClient, constants.CustomerSearchLogTag)
	if err != nil {
		return nil, err
	}
	return setTDRenewalHistory(resp), nil
}

func setTDRenewalHistory(data *accountServiceAPI.DepositsParameterRenewalHistoryResponse) map[string]interface{} {
	result := make(map[string]interface{})
	if data.Length == 0 {
		return result
	}

	var finalData []map[string]interface{}
	for _, val := range data.DepositsParameterRenewal {
		curData := map[string]interface{}{
			constants.KeyRenewalHistoryTimestamp:          utils.ParsingAndFormatTime(val.DepositsRenewalHistory["valueTimestamp"], time.DateTime, constants.OnedashTimeLayout, true),
			constants.KeyRenewalHistoryAmount:             utils.HumanizeBalance(val.DepositsRenewalHistory["tdUpdatedPrincipalAmount"], false),
			constants.KeyRenewalHistoryMaturityDate:       val.DepositsRenewalHistory["tdMaturityDate"],
			constants.KeyRenewalHistoryApplicableInterest: utils.FormatWithPercentage(utils.HumanizeBalance(val.DepositsRenewalHistory["tdAplicableInterest"], false)),
		}
		finalData = append(finalData, curData)
	}
	result[constants.KeyTermDepositRenewalHistory] = finalData

	return result
}

func (p process) GetTransactionConfigurationData(ctx context.Context, req *api.CustomerSearchRequest, user *permissionManagementStorage.UserDTO) (map[string]interface{}, error) {
	var safeID string

	if req.IdentifierType == api.IdentifierType_SAFE_ID {
		safeID = req.Identifier
	} else {
		data, err := p.getGlobalDataByIdentifier(ctx, req.Identifier, req.IdentifierType, constants.DataTypeSafeID)
		if err != nil {
			return nil, err
		}
		safeID = data
	}

	switch req.Key {
	case constants.KeyCustomerLevelTxLimit:
		return p.GetCustomerLevelTxLimitHistory(ctx, safeID)
	case constants.KeyBankWideLevelTxLimit:
		return p.GetBankLevelTxLimitHistory(ctx, user.Email)
	}

	return nil, nil
}

func (p process) GetCustomerLevelTxLimitHistory(ctx context.Context, safeID string) (map[string]interface{}, error) {
	request := &txnLimitAPI.GetTransactionLimitRequestV2{
		LimitNames: []interface{}{constants.FetchTransferLimitNameBIFast},
	}
	resp, err := helper.FetchCustomerTransferLimit(ctx, request, safeID, p.TransactionLimitClient, constants.CustomerSearchLogTag)
	if err != nil {
		return nil, err
	}

	result := make(map[string]interface{})
	if len(resp.Data) == 0 {
		return result, nil
	}

	var finalData []map[string]interface{}
	for _, val := range resp.Data {
		curData := map[string]interface{}{
			constants.KeyCustLimitTimestamp:   val.CreatedAt,
			constants.KeyCustLimitType:        constants.MappingTxLimitNameAndFrequency[val.LimitName],
			constants.KeyCustLimitFrequency:   constants.MappingTxLimitNameAndFrequency[val.Frequency],
			constants.KeyCustLimitMinAmount:   utils.HumanizeBalance(val.MinimumAmount, true),
			constants.KeyCustLimitMaxAmount:   utils.HumanizeBalance(val.MaximumAmount, true),
			constants.KeyCustLimitMinAllowed:  utils.HumanizeBalance(val.MinimumAllowed, true),
			constants.KeyCustLimitMaxAllowed:  utils.HumanizeBalance(val.MaximumAllowed, true),
			constants.KeyCustCumulativeAmount: utils.HumanizeBalance(val.CumulativeAmount, true),
		}
		finalData = append(finalData, curData)
	}
	result[constants.KeyCustomerLevelTxLimit] = finalData

	return result, nil
}

func (p process) GetBankLevelTxLimitHistory(ctx context.Context, email string) (map[string]interface{}, error) {
	request := &txnLimitAPI.GetTransactionLimitRulesRequest{
		LimitNames:        []string{constants.FetchTransferLimitNameBIFast},
		LimitRuleStatuses: []string{constants.LimitStatusActive},
	}

	resp, err := helper.GetTransferLimit(ctx, request, email, p.AppConfig.PaymentServiceConfig.PartnerID, p.TransactionLimitClient, constants.CustomerSearchLogTag)
	if err != nil {
		return nil, err
	}

	result := make(map[string]interface{})
	if len(resp.Data) == 0 {
		return result, nil
	}

	var finalData []map[string]interface{}
	for _, val := range resp.Data {
		curData := map[string]interface{}{
			constants.KeyBankLimitTimestamp:  val.CreatedAt,
			constants.KeyBankLimitType:       constants.MappingTxLimitNameAndFrequency[val.LimitName],
			constants.KeyBankLimitFrequency:  constants.MappingTxLimitNameAndFrequency[val.Frequency],
			constants.KeyBankLimitMinAmount:  utils.HumanizeBalance(val.MinimumAmount, true),
			constants.KeyBankLimitMaxAmount:  utils.HumanizeBalance(val.MaximumAmount, true),
			constants.KeyBankLimitMinAllowed: utils.HumanizeBalance(val.MinimumAllowed, true),
			constants.KeyBankLimitMaxAllowed: utils.HumanizeBalance(val.MaximumAllowed, true),
			constants.KeyBankLimitStatus:     val.Status,
		}
		finalData = append(finalData, curData)
	}
	result[constants.KeyBankWideLevelTxLimit] = finalData

	return result, nil
}

func (p process) GetExternalLinkageData(ctx context.Context, req *api.CustomerSearchRequest) (map[string]interface{}, error) {
	var safeID string
	if req.IdentifierType == api.IdentifierType_SAFE_ID {
		safeID = req.Identifier
	} else {
		data, err := p.getGlobalDataByIdentifier(ctx, req.Identifier, req.IdentifierType, constants.DataTypeSafeID)
		if err != nil {
			return nil, err
		}
		safeID = data
	}

	partners, err := helper.GetListPartner(ctx, safeID, p.PayAuthZClient, constants.CustomerSearchLogTag)
	if err != nil {
		return nil, err
	}

	linkedAcc, errCheck := p.GetLinkedAccountByPartner(ctx, safeID)
	if errCheck != nil {
		slogwrapper.FromContext(ctx).Error(constants.CustomerSearchLogTag, "error to get ecosystem linked account : "+errCheck.Error(), utils.GetTraceID(ctx))
	}
	onboardingChannel, errCheck := p.GetOnboardingChannelForEcosystem(ctx, safeID)
	if errCheck != nil {
		slogwrapper.FromContext(ctx).Error(constants.CustomerSearchLogTag, "error to get ecosystem onboarding channel : "+errCheck.Error(), utils.GetTraceID(ctx))
	}
	result := map[string]interface{}{
		req.Key: composeRequestPartnerDetail(partners, linkedAcc, onboardingChannel),
	}

	return result, nil
}

func composeRequestPartnerDetail(data *PayAuthZApi.ListPartnerResponse, linkedAcc map[string]interface{}, onboardingChannel string) map[string]interface{} {
	partners := make([]interface{}, 0)
	partnerData := make([]map[string]interface{}, 0)

	if data == nil || len(data.ListPartner) == 0 {
		return make(map[string]interface{})
	}

	for _, val := range data.ListPartner {
		curPartner := map[string]interface{}{
			constants.KeyEcosystemPartnerUserID: val.BillingAgreementID,
			constants.KeyEcosystemPartnerName:   val.PartnerName,
			constants.KeyEcosystemPartnerID:     val.PartnerID,
		}
		partners = append(partners, curPartner)
		showOvoReauth := val.PartnerName == constants.PartnerNameOvo

		partnerDetail := map[string]interface{}{
			constants.KeyEcosystemPartnerName:             val.PartnerName,
			constants.KeyEcosystemPartnerID:               val.PartnerID,
			constants.KeyEcosystemPartnerUserID:           val.BillingAgreementID,
			constants.KeyEcosystemPartnerDailyTxLimitSet:  utils.HumanizeBalance(val.DailyTransactionLimit, true),
			constants.KeyEcosystemPartnerDailyTxLimitUsed: utils.HumanizeBalance(val.UsedDailyTransactionLimit, true),
			constants.KeyEcosystemPartnerLinkedAccount:    linkedAcc[val.PartnerName],
			constants.KeyEcosystemPartnerUnlinkReason:     "", // TODO
			constants.KeyEcosystemPartnerStatusNotes:      getBillingAgreementStatusNotes(val.Status),
			constants.KeyEcosystemPartnerStatus:           val.Status,
			constants.KeyEcosystemOnboardingChannel:       onboardingChannel,
			constants.KeyLinkingHistory:                   true,
			constants.KeyReauthHistory:                    showOvoReauth,
		}
		partnerData = append(partnerData, partnerDetail)
	}

	result := map[string]interface{}{
		"partners": partners,
		"data":     partnerData,
	}
	return result
}

// getBillingAgreementStatusNotes return mapping of the billing agreement status to the expected value to be displayed
func getBillingAgreementStatusNotes(status string) string {
	if note, ok := constants.BillingAgreementStatusNotesMap[status]; ok {
		return note
	}
	return status
}

// getAroMechanismMap return mapping of the aro mechanism
func getAroMechanismMap(aro string) string {
	if aroMap, ok := constants.AroMechanismMap[aro]; ok {
		return aroMap
	}
	return aro
}

// GetLinkingHistory ...
func (p process) GetLinkingHistory(ctx context.Context, req *api.CustomerSearchRequest) (map[string]interface{}, error) {
	var safeID string
	if req.IdentifierType == api.IdentifierType_SAFE_ID {
		safeID = req.Identifier
	} else {
		data, err := p.getGlobalDataByIdentifier(ctx, req.Identifier, req.IdentifierType, constants.DataTypeSafeID)
		if err != nil {
			return nil, err
		}
		safeID = data
	}

	pageSize, _ := strconv.ParseInt(req.Payload["pageSize"], 10, 32)
	if pageSize == 0 {
		pageSize = constants.DefaultPaginationSize
	}

	partnerID := req.Payload[constants.KeyEcosystemPartnerID]
	if partnerID == "" {
		return nil, errorwrapper.Error(apiError.BadRequest, "need partner user id as payload to request")
	}

	request := &customerJournalAPI.Request{
		UserSafeID:     fmt.Sprintf("%s_%s", safeID, partnerID),
		PageSize:       int32(pageSize),
		StartDate:      req.Payload["startDate"],
		EndDate:        req.Payload["endDate"],
		StartingBefore: req.Payload["startingBefore"],
		EndingAfter:    req.Payload["endingAfter"],
		Endpoint:       constants.CustomerJournalLogTypeEcosystem,
	}
	resp, err := helper.GetEventLog(ctx, request, constants.CustomerSearchLogTag, p.CustomerJournalClient.GetCustomerJournalData)
	if err != nil {
		return nil, err
	}
	return composeEcosystemEventLog(ctx, resp), nil
}

func composeEcosystemEventLog(ctx context.Context, resp *helper.EventLogResponse) map[string]interface{} {
	result := make(map[string]interface{})

	var finalData []map[string]interface{}
	if listData, listOk := resp.Data.([]interface{}); listOk {
		for _, item := range listData {
			var data, metadata, deviceInfo map[string]interface{}
			if convData, ok := item.(map[string]interface{}); ok {
				data = convData
			}
			if convMetadata, ok := data["metadata"].(map[string]interface{}); ok {
				metadata = convMetadata
			}
			if convDeviceInfo, ok := metadata["deviceInfo"].(map[string]interface{}); ok {
				deviceInfo = convDeviceInfo
			}

			currentData := map[string]interface{}{
				constants.KeyLinkingHistoryTimestamp:     transformEpochTimestamp(ctx, data["eventTimestamp"]),
				constants.KeyLinkingHistoryPreviousLimit: utils.HumanizeBalance(metadata["previousLimit"].(string), true),
				constants.KeyLinkingHistoryUpdatedLimit:  utils.HumanizeBalance(metadata["currentLimit"].(string), true),
				constants.KeyLinkingHistoryAccountID:     metadata["accountID"],
				constants.KeyLinkingHistoryAction:        metadata["action"],
				constants.KeyLinkingHistoryUnlinkReason:  metadata["unlinkReason"],
				constants.KeyLinkingHistoryActionBy:      constructEcosystemActionBy(metadata["actionBy"]),
				constants.KeyLinkingHistoryActionSystem:  metadata["actionSystem"],
				constants.KeyLinkingHistoryActionStatus:  metadata["status"],
				constants.KeyLinkingHistoryPartnerName:   metadata["partnerName"],
				constants.KeyLinkingHistoryDeviceID:      deviceInfo["deviceID"],
				constants.KeyLinkingHistoryDeviceBrand:   deviceInfo["deviceBrand"],
				constants.KeyLinkingHistoryDeviceModel:   deviceInfo["deviceModel"],
				constants.KeyLinkingHistoryOSName:        deviceInfo["deviceOSName"],
				constants.KeyLinkingHistoryOSVersion:     deviceInfo["deviceOSVersion"],
				constants.KeyLinkingErrorMessage:         metadata["failureReason"],
			}
			finalData = append(finalData, currentData)
		}
	}

	result[constants.KeyLinkingHistory] = composeTableWithPaginationResponse(finalData, resp.Pagination)
	return result
}

func constructEcosystemActionBy(actionBy interface{}) string {
	if action, ok := actionBy.(string); ok {
		if action == "" {
			return ""
		}
		splits := strings.Split(action, "-")
		if strings.Contains(action, "Ops") {
			return fmt.Sprintf("Ops - %s", splits[len(splits)-1])
		} else if strings.Contains(action, "CRM") {
			return fmt.Sprintf("CRM - %s", splits[len(splits)-1])
		} else {
			return splits[len(splits)-1]
		}
	}

	return ""
}

// GetReauthenticationHistory ...
func (p process) GetReauthenticationHistory(ctx context.Context, req *api.CustomerSearchRequest) (map[string]interface{}, error) {
	var safeID string
	if req.IdentifierType == api.IdentifierType_SAFE_ID {
		safeID = req.Identifier
	} else {
		data, err := p.getGlobalDataByIdentifier(ctx, req.Identifier, req.IdentifierType, constants.DataTypeSafeID)
		if err != nil {
			return nil, err
		}
		safeID = data
	}

	pageSize, _ := strconv.ParseInt(req.Payload["pageSize"], 10, 32)
	if pageSize == 0 {
		pageSize = constants.DefaultPaginationSize
	}

	request := &customerJournalAPI.Request{
		UserSafeID:     safeID,
		PageSize:       int32(pageSize),
		StartDate:      req.Payload["startDate"],
		EndDate:        req.Payload["endDate"],
		StartingBefore: req.Payload["startingBefore"],
		EndingAfter:    req.Payload["endingAfter"],
		Endpoint:       constants.CustomerJournalLogTypeOvoNabung,
	}
	resp, err := helper.GetEventLog(ctx, request, constants.CustomerSearchLogTag, p.CustomerJournalClient.GetCustomerJournalData)
	if err != nil {
		return nil, err
	}
	return composeReauthenticationEventLog(ctx, resp), nil
}

func composeTableWithPaginationResponse(data []map[string]interface{}, pagination interface{}) map[string]interface{} {
	result := map[string]interface{}{
		"data":       data,
		"pagination": pagination,
	}
	return result
}

func composeReauthenticationEventLog(ctx context.Context, resp *helper.EventLogResponse) map[string]interface{} {
	result := make(map[string]interface{})

	var finalData []map[string]interface{}
	if listData, listOk := resp.Data.([]interface{}); listOk {
		for _, item := range listData {
			var data, metadata map[string]interface{}
			if convData, ok := item.(map[string]interface{}); ok {
				data = convData
			}
			if convMetadata, ok := data["metadata"].(map[string]interface{}); ok {
				metadata = convMetadata
			}
			currentData := map[string]interface{}{
				constants.KeyReauthHistoryTimestamp:        transformEpochTimestamp(ctx, data["eventTimestamp"]),
				constants.KeyReauthHistoryPrevMobileNumber: metadata["oldPhoneNumber"],
				constants.KeyReauthHistoryNewMobileNumber:  metadata["newPhoneNumber"],
				constants.KeyReauthHistoryStatus:           metadata["status"],
				constants.KeyReauthHistoryStatusReason:     metadata["statusReason"],
			}
			finalData = append(finalData, currentData)
		}
	}

	result[constants.KeyReauthHistory] = composeTableWithPaginationResponse(finalData, resp.Pagination)
	return result
}

func (p process) GetLinkedAccountByPartner(ctx context.Context, safeID string) (map[string]interface{}, error) {
	linkedAccMapping := make(map[string]interface{})
	// get from redis
	redisKey := fmt.Sprintf("%s_%s_%s", constants.RedisFirstKeyCustomerDataPoint, constants.KeyEcosystemPartnerLinkedAccount, safeID)

	// get redisKey
	redisData, err := getCustomerSearchDataPointFromRedis(ctx, redisKey, constants.CustomerSearchLogTag)
	if err != nil {
		return nil, err
	}
	if redisData != "" {
		errMarshal := json.Unmarshal([]byte(redisData), &linkedAccMapping)
		if errMarshal != nil {
			return nil, errorwrapper.WrapError(errMarshal, apiError.InternalServerError, "failed to unmarshal data")
		}
		return linkedAccMapping, nil
	}

	// get from API
	cifNumber, err := p.getGlobalDataByIdentifier(ctx, safeID, api.IdentifierType_SAFE_ID, constants.DataTypeCif)
	if err != nil {
		return linkedAccMapping, err
	}

	request := &accountServiceAPI.ListCASAAccountsForCustomerDetailRequest{
		CifNumber: cifNumber,
	}

	resp, err := helper.GetAccountList(ctx, request, p.AccountServiceClient, constants.CustomerSearchLogTag)
	if err != nil {
		return linkedAccMapping, err
	}

	filteredAcc := filterByVariantForFunding(resp.Accounts, true)
	// get grab
	if len(filteredAcc[constants.ProductVariantMainCasa]) > 0 {
		mainCasa := filteredAcc[constants.ProductVariantMainCasa]
		if acc, ok := mainCasa[len(mainCasa)-1].(map[string]interface{}); ok {
			linkedAccMapping[constants.PartnerNameGrab] = acc["accountID"]
		}
	}
	// get ovo
	if len(filteredAcc[constants.ProductVariantOvoNabung]) > 0 {
		for _, item := range filteredAcc[constants.ProductVariantOvoNabung] {
			if acc, ok := item.(map[string]interface{}); ok {
				if acc["status"] != constants.AccountStatusClosed {
					linkedAccMapping[constants.PartnerNameOvo] = acc["accountID"]
				}
			}
		}
	}
	// set to redis
	errRedis := setCustomerSearchDataPointToRedis(ctx, redisKey, linkedAccMapping, constants.CustomerSearchDataPointLogTag, constants.MappingExpiryTimeForDataPoint[constants.KeyEcosystemPartnerLinkedAccount])
	if errRedis != nil {
		return linkedAccMapping, errorwrapper.WrapError(err, apiError.InternalServerError, "error set data point to redis")
	}

	return linkedAccMapping, nil
}

func (p process) GetOnboardingChannelForEcosystem(ctx context.Context, safeID string) (string, error) {
	// get from redis
	redisKey := fmt.Sprintf("%s_%s_%s", constants.RedisFirstKeyCustomerDataPoint, constants.KeyEcosystemOnboardingChannel, safeID)

	// get redisKey
	redisData, err := getCustomerSearchDataPointFromRedis(ctx, redisKey, constants.CustomerSearchLogTag)
	if err != nil {
		return "", err
	}
	if redisData != "" {
		var onboardingChannel string
		errMarshal := json.Unmarshal([]byte(redisData), &onboardingChannel)
		if errMarshal != nil {
			return "", errorwrapper.WrapError(errMarshal, apiError.InternalServerError, "failed to unmarshal data")
		}
		return onboardingChannel, nil
	}

	// get from API
	resp, err := p.GetCustomerByIdentifier(ctx, safeID, string(api.IdentifierType_SAFE_ID), 1)
	if err != nil {
		return "", errorwrapper.WrapError(err, apiError.InternalServerError, "error getting customer related data by identifier")
	}

	var onboardingChannel string
	onboardingInfo := resp.Items[0].Applications
	if len(onboardingInfo) == 0 {
		return "", nil
	}
	onboardingChannel = string(onboardingInfo[0].Channel)

	// set to redis
	errRedis := setCustomerSearchDataPointToRedis(ctx, redisKey, onboardingChannel, constants.CustomerSearchLogTag, constants.MappingExpiryTimeForDataPoint[constants.KeyEcosystemOnboardingChannel])
	if errRedis != nil {
		return "", errorwrapper.WrapError(err, apiError.InternalServerError, "error set data point to redis")
	}

	return onboardingChannel, nil
}

func transformEpochTimestamp(ctx context.Context, timestamp interface{}) string {
	if eventTimestamp, ok := timestamp.(string); ok {
		intEventTimestamp, err := strconv.ParseInt(eventTimestamp, 10, 64)
		if err != nil {
			slogwrapper.FromContext(ctx).Warn(constants.CustomerSearchLogTag, "error to parse eventTimestamp : "+err.Error(), utils.GetTraceID(ctx))
			return ""
		}

		milliseconds := intEventTimestamp / 1e6 // Convert nanoseconds to milliseconds
		t := time.Unix(0, milliseconds*int64(time.Millisecond))
		return t.Format(constants.ISO8601Layout)
	}
	return ""
}
