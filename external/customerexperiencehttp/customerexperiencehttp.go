// Package customerexperiencehttp provides functionalities to call customer-experience services
package customerexperiencehttp

import (
	"context"
	"encoding/json"
	"net/http"
	"strings"
	"time"

	customerExperienceAPI "gitlab.myteksi.net/bersama/customer-experience/api"
	"gitlab.myteksi.net/dakota/klient"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

var (
	getVideoCallOpsDescriptor = &klient.EndpointDescriptor{
		Service: service,
		Name:    "getVideoCallOps",
		Method:  http.MethodGet,
		Path:    "/api/v1/video-call/ops/:applicationID",
	}
	getCustomerHistoryLogDescriptor = &klient.EndpointDescriptor{
		Service: service,
		Name:    "getCustomerHistoryLog",
		Method:  http.MethodGet,
		Path:    "/api/v1/customers/history-logs",
	}
)

// VideoCallRecordingFile represents a video recording with its type and URL.
type VideoCallRecordingFile struct {
	Type string `json:"type"`
	URL  string `json:"url"`
}

// VideoCallDetails contains information about the video call session.
type VideoCallDetails struct {
	Purpose          string                 `json:"purpose"`
	Description      string                 `json:"description"`
	Reason           string                 `json:"reason"`
	Assignee         string                 `json:"assignee"`
	VideoCallCreated string                 `json:"videoCallCreated"`
	VideoCallEnded   string                 `json:"videoCallEnded"`
	Duration         int                    `json:"duration"`
	RecordingFile    VideoCallRecordingFile `json:"recordingFile"`
}

// VideoCallApprovalHistory captures approval actions for a video call request.
type VideoCallApprovalHistory struct {
	User      string `json:"user"`
	Ticket    string `json:"ticket"`
	Role      string `json:"role"`
	Status    string `json:"status"`
	Notes     string `json:"notes"`
	Timestamp string `json:"timestamp"`
}

// VideoCallSupportingDocument represents a document that supports a ticket.
type VideoCallSupportingDocument struct {
	Type string `json:"type"`
	URL  string `json:"url"`
}

// VideoCallTicket represents a video call ticket and its associated data.
type VideoCallTicket struct {
	CustomerID          string                        `json:"customerID"`
	ApplicationID       string                        `json:"applicationID"`
	TicketNumber        string                        `json:"ticketNumber"`
	TicketStatus        string                        `json:"ticketStatus"`
	SupportingDocuments []VideoCallSupportingDocument `json:"supportingDocuments"`
	ApprovalHistory     []VideoCallApprovalHistory    `json:"approvalHistory"`
	Questions           []interface{}                 `json:"questions"` // Replace with actual type if available
	Details             VideoCallDetails              `json:"details"`
}

// GetVideoCallOpsResponse wraps the result of a GetVideoCallOps request.
type GetVideoCallOpsResponse struct {
	Items []VideoCallTicket
}

// GetCustomerHistoryLogResponse ...
type GetCustomerHistoryLogResponse struct {
	CustomerID  string       `json:"customerID"`
	StartDate   time.Time    `json:"startDate"`
	EndDate     time.Time    `json:"endDate"`
	Count       int          `json:"count"`
	HistoryLogs []HistoryLog `json:"historyLogs"`
}

type HistoryLog struct {
	CustomerID     string               `json:"customerId"`
	TableName      string               `json:"tableName"`
	Status         string               `json:"status"`
	DetailStatus   string               `json:"detailStatus"`
	PreviousValues *json.RawMessage     `json:"previousValues,omitempty"`
	NewValues      *json.RawMessage     `json:"newValues,omitempty"`
	CustomFields   *HistoryCustomFields `json:"customFields,omitempty"`
	Remarks        string               `json:"remarks"`
	ApprovedBy     string               `json:"approvedBy"`
	Source         string               `json:"source"`
	UpdatedAt      time.Time            `json:"updatedAt"`
	UpdatedBy      string               `json:"updatedBy"`
}

type HistoryCustomFields struct {
	CaseID string `json:"case_id"`
}

type HistoryAgreement struct {
	AgreementID     string `json:"agreementID"`
	AgreementType   string `json:"agreementType"`
	AgreementStatus string `json:"agreementStatus"`
}

type HistoryFieldValues struct {
	// Address-related
	RT             string `json:"RT,omitempty"`
	RW             string `json:"RW,omitempty"`
	Street         string `json:"street,omitempty"`
	City           string `json:"city,omitempty"`
	Province       string `json:"province,omitempty"`
	PostalCode     string `json:"postalCode,omitempty"`
	Subdistrict    string `json:"subdistrict,omitempty"`
	Village        string `json:"village,omitempty"`
	AddressType    string `json:"addressType,omitempty"`
	CityKey        string `json:"cityKey,omitempty"`
	ProvinceKey    string `json:"provinceKey,omitempty"`
	SubdistrictKey string `json:"subdistrictKey,omitempty"`
	VillageKey     string `json:"villageKey,omitempty"`
	PostalCodeKey  string `json:"postalCodeKey,omitempty"`

	// Identity-related
	IDType   string `json:"IDType,omitempty"`
	IDNumber string `json:"IDNumber,omitempty"`

	// Employment-related
	EmploymentType string `json:"employmentType,omitempty"`
	Industry       string `json:"industry,omitempty"`
	JobPosition    string `json:"jobPosition,omitempty"`
	MonthlyIncome  string `json:"monthlyIncome,omitempty"`
	Occupation     string `json:"occupation,omitempty"`
	SourceOfFunds  string `json:"sourceOfFunds,omitempty"`

	// Account-related
	PurposeOfAccount string `json:"purposeOfAccount,omitempty"`

	// Retail Customer-related
	Alias            string `json:"alias,omitempty"`
	MaritalStatus    string `json:"maritalStatus,omitempty"`
	MotherMaidenName string `json:"motherMaidenName,omitempty"`
	Name             string `json:"name,omitempty"`
	FullName         string `json:"fullName,omitempty"`

	// Nested customFields string in failed cases
	CustomFieldsRaw string `json:"customFields,omitempty"`
}

const (
	// UserIDHeaderKey is the HTTP header key for the user ID.
	UserIDHeaderKey = "X-Grab-Id-Userid"
)

// replacePathParam replaces the placeholder in the path with the actual value.
func replacePathParam(path, key, value string) string {
	placeholder := ":" + key
	return strings.Replace(path, placeholder, value, 1)
}

// GetVideoCallOps fetches video call operations based on the given application ID and user ID.
func (c *CustomerExperienceClient) GetVideoCallOps(ctx context.Context, applicationID string, userID string) (*GetVideoCallOpsResponse, error) {
	slog.FromContext(ctx).Info(logTag, "calling customerExperience GetVideoCallOps")

	var tickets []VideoCallTicket
	ctx = klient.MakeContext(ctx, getVideoCallOpsDescriptor)

	headers := http.Header{
		UserIDHeaderKey: []string{userID},
	}

	getVideoCallOpsDescriptor.Path = replacePathParam(getVideoCallOpsDescriptor.Path, "applicationID", applicationID)

	req := &klientRequest{
		ctx:            ctx,
		descriptor:     getVideoCallOpsDescriptor,
		requestHeaders: headers,
	}

	err := c.machinery.RoundTrip(ctx, req, &klientResponse{responseBody: &tickets})

	kResp := &GetVideoCallOpsResponse{
		Items: tickets,
	}

	if err != nil {
		slog.FromContext(ctx).Info(logTag, "failed to call customerExperience GetVideoCallOps", slog.Error(err))
		return kResp, err
	}

	return kResp, nil
}

// GetCustomerHistoryLog fetches customer history log by customer id
func (c *CustomerExperienceClient) GetCustomerHistoryLog(ctx context.Context, payload customerExperienceAPI.GetHistoryLogRequest) (*GetCustomerHistoryLogResponse, error) {
	slog.FromContext(ctx).Info(logTag, "calling customerExperience GetCustomerHistoryLog")

	kResp := &GetCustomerHistoryLogResponse{}

	ctx = klient.MakeContext(ctx, getCustomerHistoryLogDescriptor)

	req := &klientRequest{
		ctx:         ctx,
		descriptor:  getCustomerHistoryLogDescriptor,
		requestBody: payload,
	}

	err := c.machinery.RoundTrip(ctx, req, &klientResponse{responseBody: &kResp})

	if err != nil {
		slog.FromContext(ctx).Info(logTag, "failed to call customerexperienceHTTP getCustomerHistory",
			slog.Error(err))
		return kResp, err
	}

	return kResp, nil
}
