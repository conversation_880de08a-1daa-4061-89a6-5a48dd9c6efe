package custom

import (
	"context"

	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/permissionmanagement/api"
	"gitlab.myteksi.net/bersama/common/validations"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"
)

// UsersFilterValidation ...
func UsersFilterValidation(fl validations.FieldLevel) bool {
	return DefaultFilterValidation(fl, []string{"status", "role_id"}, "UsersFilterValidation")
}

// UsersSortByValidation ...
func UsersSortByValidation(fl validations.FieldLevel) bool {
	return DefaultSortByValidation(fl, []string{"id", "name", "created_at", "updated_at", "status", "email"}, "UsersSortValidation")
}

// DefaultFilterValidation ...
func DefaultFilterValidation(fl validations.FieldLevel, allowedFilterColumn []string, eventType string) bool {
	filter, ok := fl.Field().Interface().([]api.Filter)
	if !ok {
		slog.FromContext(context.Background()).Error(eventType, "error getting filter")
		return false
	}

	for _, field := range filter {
		if field.Column != "" && !utils.SliceContains(allowedFilterColumn, field.Column) {
			slog.FromContext(context.Background()).Error(eventType, "invalid column")
			return false
		}
	}

	return true
}

// DefaultSortByValidation ...
func DefaultSortByValidation(fl validations.FieldLevel, allowedSortColumn []string, eventType string) bool {
	sort, ok := fl.Field().Interface().(api.Sort)
	if !ok {
		slog.FromContext(context.Background()).Error(eventType, "error getting filter")
		return false
	}

	if sort.Column != "" && !utils.SliceContains(allowedSortColumn, sort.Column) {
		slog.FromContext(context.Background()).Error(eventType, "invalid column")
		return false
	}

	return true
}
