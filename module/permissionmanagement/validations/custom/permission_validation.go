//nolint:golint,typecheck
package custom

import (
	"context"

	"gitlab.com/gx-regional/dbmy/ops-support/onedash-be/permissionmanagement/api"
	"gitlab.myteksi.net/bersama/common/validations"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.super-id.net/bersama/opsce/onedash-be/utils"
)

// PermissionsFilterValidation ...
func PermissionsFilterValidation(fl validations.FieldLevel) bool {
	allowedFilterColumn := []string{"status", "module_id"}

	filters, ok := fl.Field().Interface().([]api.Filter)
	if !ok {
		slog.FromContext(context.Background()).Error("PermissionsFilterValidation", "error getting filter")
		return false
	}

	for _, filter := range filters {
		if filter.Column != "" && !utils.SliceContains(allowedFilterColumn, filter.Column) {
			slog.FromContext(context.Background()).Error("PermissionsFilterValidation", "invalid column")
			return false
		}
	}

	return true
}

// PermissionsSortByValidation ...
func PermissionsSortByValidation(fl validations.FieldLevel) bool {
	allowedSortColumn := []string{"id", "name", "created_at", "updated_at"}

	sort, ok := fl.Field().Interface().(api.Sort)
	if !ok {
		slog.FromContext(context.Background()).Error("PermissionsSortByValidation", "error getting filter")
		return false
	}

	if sort.Column != "" && !utils.SliceContains(allowedSortColumn, sort.Column) {
		slog.FromContext(context.Background()).Error("PermissionsSortByValidation", "invalid column")
		return false
	}

	return true
}

// PermissionBitwiseValidation ...
func PermissionBitwiseValidation(fl validations.FieldLevel) bool {
	bitwise, ok := fl.Field().Interface().(int64)
	if !ok {
		slog.FromContext(context.Background()).Error("PermissionBitwiseValidation", "error getting bitwise")
		return false
	}

	if bitwise <= 0 || (bitwise&(bitwise-1)) != 0 {
		slog.FromContext(context.Background()).Error("PermissionsSortByValidation", "invalid bitwise value")
		return false
	}

	return true
}
