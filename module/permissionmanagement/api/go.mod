module gitlab.com/gx-regional/dbmy/ops-support/onedash-be/permissionmanagement/api

go 1.23.0

toolchain go1.23.7

replace (
	golang.org/x/crypto => golang.org/x/crypto v0.39.0
	golang.org/x/text => golang.org/x/text v0.21.0
	gopkg.in/yaml.v2 => gopkg.in/yaml.v2 v2.4.0
)

require (
	github.com/json-iterator/go v1.1.12
	github.com/stretchr/testify v1.7.0
	gitlab.myteksi.net/dakota/klient v1.5.6
)

require (
	github.com/DataDog/datadog-agent/pkg/obfuscate v0.0.0-20211129110424-6491aa3bf583 // indirect
	github.com/DataDog/datadog-go v4.8.2+incompatible // indirect
	github.com/DataDog/datadog-go/v5 v5.0.2 // indirect
	github.com/DataDog/sketches-go v1.0.0 // indirect
	github.com/Microsoft/go-winio v0.5.1 // indirect
	github.com/cactus/go-statsd-client/statsd v0.0.0-20191106001114-12b4e2b38748 // indirect
	github.com/cespare/xxhash/v2 v2.1.1 // indirect
	github.com/davecgh/go-spew v1.1.1 // indirect
	github.com/dgraph-io/ristretto v0.1.0 // indirect
	github.com/dustin/go-humanize v1.0.0 // indirect
	github.com/golang/glog v0.0.0-20160126235308-23def4e6c14b // indirect
	github.com/google/uuid v1.3.0 // indirect
	github.com/hashicorp/errwrap v1.0.0 // indirect
	github.com/hashicorp/go-multierror v1.1.0 // indirect
	github.com/josharian/intern v1.0.0 // indirect
	github.com/mailru/easyjson v0.7.7 // indirect
	github.com/modern-go/concurrent v0.0.0-20180228061459-e0a39a4cb421 // indirect
	github.com/modern-go/reflect2 v1.0.2 // indirect
	github.com/myteksi/hystrix-go v1.1.3 // indirect
	github.com/philhofer/fwd v1.1.1 // indirect
	github.com/pkg/errors v0.9.1 // indirect
	github.com/pmezard/go-difflib v1.0.0 // indirect
	github.com/rcrowley/go-metrics v0.0.0-20201227073835-cf1acfcdf475 // indirect
	github.com/stretchr/objx v0.3.0 // indirect
	github.com/tinylib/msgp v1.1.2 // indirect
	gitlab.myteksi.net/dakota/common/context v0.3.0 // indirect
	gitlab.myteksi.net/dakota/common/tracing v1.5.0 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/config/mode v1.0.0 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/log/internal/logdefaults v1.0.0 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/log/logging v1.1.5 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/log/timerlog v1.0.0 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/log/yall v1.0.11 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/monitor/statsd v1.0.5 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/parallel/gconcurrent v1.0.0 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/resilience/circuitbreaker v1.0.3 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/resilience/hystrix v1.0.0 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/tags v1.1.7 // indirect
	gitlab.myteksi.net/gophers/go/staples/logging v1.0.0 // indirect
	gitlab.myteksi.net/gophers/go/staples/statsd v1.0.6 // indirect
	go.uber.org/atomic v1.6.0 // indirect
	go.uber.org/multierr v1.5.0 // indirect
	go.uber.org/zap v1.16.0 // indirect
	golang.org/x/net v0.25.0 // indirect
	golang.org/x/sys v0.33.0 // indirect
	golang.org/x/time v0.0.0-20211116232009-f0f3c7e86c11 // indirect
	golang.org/x/xerrors v0.0.0-20191204190536-9bdfabe68543 // indirect
	google.golang.org/protobuf v1.26.0 // indirect
	gopkg.in/DataDog/dd-trace-go.v1 v1.36.2 // indirect
	gopkg.in/yaml.v3 v3.0.0-20210107192922-496545a6307b // indirect
)
